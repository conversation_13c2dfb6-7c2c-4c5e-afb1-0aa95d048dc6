{"[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.quickSuggestions": {"strings": true}, "workbench.colorTheme": "One Monokai", "editor.tabSize": 2, "editor.detectIndentation": false, "emmet.triggerExpansionOnTab": true, "editor.formatOnSave": true, "javascript.format.enable": true, "git.enableSmartCommit": true, "git.autofetch": true, "git.confirmSync": false, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "liveServer.settings.donotShowInfoMsg": true, "explorer.confirmDelete": false, "javascript.updateImportsOnFileMove.enabled": "always", "typescript.updateImportsOnFileMove.enabled": "always", "files.exclude": {"**/.idea": true}, "editor.codeActionsOnSave": {"source.fixAll.stylelint": true, "source.fixAll.eslint": true}, "stylelint.validate": ["html", "vue", "js", "scss"], "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[jsonc]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.suggest.snippetsPreventQuickSuggestions": false, "prettier.htmlWhitespaceSensitivity": "ignore", "prettier.vueIndentScriptAndStyle": true, "docthis.authorName": "chuzhixin <EMAIL>", "docthis.includeAuthorTag": true, "docthis.includeDescriptionTag": true, "docthis.enableHungarianNotationEvaluation": true, "docthis.inferTypesFromNames": true, "vetur.format.defaultFormatter.html": "prettier", "files.autoSave": "onFocusChange", "path-intellisense.mappings": {"@": "${workspaceRoot}/src"}, "files.eol": "\n"}