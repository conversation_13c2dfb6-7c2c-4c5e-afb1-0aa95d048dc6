{"methods": {"animation": true, "renderer": true, "light": true, "controls": true, "TWEEN": true, "statement": true, "sky": true, "postprocessing": true}, "events": {"windowResize": true}, "params": {"container": null, "renderer": ["WebGLRenderer", "CSS2<PERSON><PERSON><PERSON>", "CSS3<PERSON><PERSON><PERSON>"], "controls": ["OrbitControls", "FirstPersonControls", "PointerLockControls", "first<PERSON><PERSON>", "third<PERSON><PERSON>"], "rendererParams": {"antialias": true}, "postprocessing": ["OutlinePass", "UnrealBloomPass"]}}