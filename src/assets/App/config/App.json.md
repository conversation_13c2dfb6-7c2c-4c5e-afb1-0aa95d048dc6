```json5
/* App初始化所需参数，
  以下参数所对应方法或事件默认针对初始场景scene、相机camera及所选部分功能生效，
  如有需要修改，请取消选择并在外部调用或在外部调用时重写对应方法*/
{
  // 需要使用的方法
  "methods": {
    // 是否应用动画方法
    "animation": true,
    // 是否应用渲染方法
    "renderer": true,
    // 是否应用灯光方法
    "light": true,
    // 是否应用控制方法
    "controls": true,
    // 是否应用相机飞行方法
    "TWEEN": true,
    // 是否应用声明方法
    "statement": true,
    // 是否应用模拟天空方法
    "sky": true,
    // 是否应用后期处理
    "postprocessing": true
  },
  // 需要使用的监听事件
  "events": {
    // 是否应用窗口尺寸监听事件
    "windowResize": true
  },
  // 参数配置
  "params": {
    // 渲染结果输出dom页面
    "container": null,
    // 渲染方法使用的渲染器集合
    "renderer": [
      "WebGLRenderer",
      "CSS2DRenderer",
      "CSS3DRenderer"
    ],
    // 控制方法使用的控制器集合
    "controls": [
      "OrbitControls",
      "FirstPersonControls",
      "PointerLockControls",
      "firstPerson",
      "thirdPerson"
    ],
    // WebGLRenderer渲染器使用的json参数（具体参数自行查找）
    "rendererParams": {
      // 是否应用抗锯齿
      "antialias": true
    },
    // 后期处理方法使用的处理通道集合
    "postprocessing": [
      "OutlinePass",
      "UnrealBloomPass"
    ]
  }
}

```
