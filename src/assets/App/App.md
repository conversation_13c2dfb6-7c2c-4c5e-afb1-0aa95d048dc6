```js
/**
 * @file ThreeJs工具类
 */
class App {
    /**
     * 类初始化
     */
    constructor () {}

    /**
     * threeJs初始化
     * @param data {*}
     * @param callback function (scene, camera, renderer)
     */
    init (data, callback) {}

    /**
     * 添加方法（需添加的方法在init函数的data.methods中添加）
     * @protected
     */
    addMethods () {}

    /**
     * 加载模型
     * @param params （url, type, animations, scene） {url: 'http://www.model.cn' or {mtl: 'http://www.model.mtl.cn', obj: 'http://www.model.obj.cn'}, type: 'gltf', animations: true, scene: null}
     * @param callback
     * @param onProgress function
     * @return {obj, animations, mixer, actions}
     */
    addModel (params, callback, onProgress) {}

    /**
     * 添加模型模型监听事件
     * @param obj object 需要监听的模型集合
     * @param type str 事件类型
     * @param exist function 事件触发时，监听到模型时的触发函数
     * @param key str 事件标识符
     * @param params {*}
     * @param lose function 事件触发时，未监听到模型时的触发函数
     */
    addOn (obj, type, exist, key, params, lose) {}

    /**
     * 移除监听事件
     * @param type str 事件类型
     * @param key str 事件标识符
     * @param params {*}
     */
    rmOn (type, key, params) {}

    /**
     * 添加动画
     * @param model
     * @param animations
     * @return {mixer, actions}
     */
    addAnimation (model, animations) {}

    /**
     * 添加监听事件
     * @protected
     */
    addEvent () {}

    /**
     * 添加后置处理
     * @param callback
     * @param params {*} {scene}
     * @return {EffectComposer}
     */
    addPostprocessing (callback, params) {}

    /**
     * 添加边缘发光通道
     * @param callback
     * @param params
     * @returns {*}
     */
    addOutlinePass (callback, params) {}

    /**
     * 添加局部辉光通道
     * @param callback
     * @param params
     * @returns {*}
     */
    addUnrealBloomPass (callback, params) {}

    /**
     * 添加天空
     */
    addSky () {}

    /**
     * 添加渲染器
     * @param callback
     */
    addRenderer (callback) {}

    /**
     * 添加控制器（内部调用允许重写，外部调用允许回调）
     * @return {*}
     */
    addControls (callback) {}

    /**
     * 添加灯光（内部调用允许重写，外部调用允许回调）
     * @param params (scene)
     * @param callback
     * @return
     */
    addLight (callback, params) {}
    
    /**
     * 窗口尺寸（内部调用允许重写，外部调用允许回调）
     * @param callback
     */
    onWindowResize (callback) {}

    /**
     * 动画（内部调用允许重写，外部调用允许回调）
     * @param callback
     */
    animate (callback) {}

    /**
     * 渲染（内部调用允许重写，外部调用允许回调）
     * @param callback
     */
    render (callback) {}

    /**
     * 获取模型层级
     * @param model 模型
     * @return {*}
     */
    getModelTier (model) {}

    /**
     * 向树中每一级添加父级元素
     * @param tree Json
     * @param type String list or dict
     * @return {*}
     */
    addParentToTree (tree, type) {}

    /**
     * 创建精灵文字
     * @param text str
     * @param options (fontSize,backgroundColor,color) {fontSize:20, backgroundColor: '#fff', color: '#fff'}
     * @returns {*}
     */
    createSpriteText (text, options) {}

    /**
     * 创建精灵标签
     * @param title str 标题
     * @param content str 内容
     * @param options (fontSize,backgroundColor,color,scene,scale,position) {fontSize:20, width: 200, backgroundColor: '#fff', color: '#fff'}
     * @returns {*}
     */
    createSpriteLabel (title, content, options) {}

    /**
     * 创建div精灵标签
     * @param title str 标题
     * @param content str 内容
     * @param callback function 回调函数
     * @param options {*} (scene, dom)
     */
    createDivSpriteLabel (title, content, callback, options) {}

    /**
     * 创建2D展示标签
     * @param title
     * @param content
     * @returns {CSS2DObject}
     */
    createShow2DLabel (title, content) {}

    /**
     * 创建3D展示标签
     * @param title
     * @param content
     * @return {any}
     */
    createShow3DLabel (title, content) {}

    /**
     * 三维坐标转屏幕坐标
     * @param vectorOrObject
     * @returns {{x: number, y: number}}
     */
    vectorToScreen (vectorOrObject) {}

    /**
     * 获取与射线相交的对象数组
     * @param event
     * @param parent
     * @param recursive
     * @returns {*}
     */
    getIntersectObject (event, parent, recursive) {}

    /**
     * 从当前位置飞到指定位置
     * @param option
     * @returns {string|*}
     */
    flyTo (option) {}

    /**
     * 获取模型对象世界坐标
     * @param obj {*} 模型对象
     * @param scene {*} 模型对象所在场景
     * @return {THREE.Vector3}
     */
    getObjWorldPosition (obj, scene) {}

    /**
     * 获取两模型对象间距离
     * @param obj1 {*} 模型对象
     * @param obj2 {*} 模型对象
     * @param params {*}
     * @return {*}
     */
    getObjDistance (obj1, obj2, params) {}

    /**
     * 添加漫游（未完成）
     * @param obj
     */
    addRoam (obj) {}

    /**
     * 判断是否为移动（手机平板）设备
     * @returns {boolean}
     */
    isMobile () {}

    /**
     * 判断是否连接网络
     * @return {boolean}
     */
    isNetwork () {}

    /**
     * 获取网络连接类型
     * @return {null|*}
     */
    getNetworkType () {}

    /**
     * 寻找切换断点
     * @protected
     * @param text String 文本内容
     * @param width 每行宽度
     * @param context object
     * @return {number}
     */
    findBreakPoint (text, width, context) {}

    /**
     * 字符串按宽度拆分数组
     * @protected
     * @param text  String 文本内容
     * @param context object
     * @param width 每行宽度
     * @return {[]}
     */
    breakLinesForCanvas (text, context, width) {}

    /**
     * 声明
     */
    statement () {}
}
```
