<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1650px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="130px"
      :model="formData"
      :rules="rules"
    >
      3
      <table v-loading="formloading" class="form-table">
        <tr class="title"><td colspan="3">基本信息</td></tr>
        <tr>
          <td>
            <el-form-item label="项目编号" prop="code">
              <el-input v-model="formData.code" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目地址" prop="address">
              <el-input v-model="formData.address" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目名称" prop="name">
              <el-input v-model="formData.name" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="安装人员" prop="manager">
              <el-input v-model="formData.manager" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="项目类型" prop="projectType">
              <el-select
                v-model="formData.projectType"
                placeholder="请选择项目类型"
                style="width: 280px"
              >
                <el-option
                  v-for="item in projectTypeList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="安装时间" prop="startTime">
              <el-date-picker
                v-model="formData.startTime"
                format="yyyy-MM-dd"
                placeholder="选择时间"
                style="width: 280px"
                value-format="yyyy-MM-dd"
              />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="地理坐标" prop="position">
              <el-input v-model="formData.position" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="业主单位" prop="ownerName">
              <el-input v-model="formData.ownerName" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="是否生成日报" prop="isDaily">
              <el-radio-group
                v-model="formData.isDaily"
                size="small"
                style="width: 200px"
              >
                <el-radio-button label="y">是</el-radio-button>
                <el-radio-button label="n">否</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="预警类型" prop="alarmType">
              <el-radio-group
                v-model="formData.alarmType"
                size="small"
                style="width: 200px"
              >
                <el-radio-button label="at">自动</el-radio-button>
                <el-radio-button label="mt">手动</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </td>
          <td colspan="2">
            <el-form-item label="介绍" prop="introduce">
              <el-input
                v-model="formData.introduce"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 900px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <!-- 附件上传 -->
    <UploadLargeFileFdfsPopup
      ref="UploadLargeFileFdfsPopup"
      @getFilesInfo="getFilesInfo"
    />
    <BMapPopup ref="BMapPopup" @callBackSite="callBackSite" />
    <baiduMap ref="baiduMap" />
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>

    <!--    <CommonDepartPopup
      ref="CommonDepartPopup"
      @callBackDepart="callBackDepart"
    /> -->
    <!-- <CommonRolePopup ref="CommonRolePopup" @callBackRole="callBackRole" /> -->
    <!-- <CommonUploadLargeFileFdfsPopup  ref="CommonUploadLargeFileFdfsPopup" /> -->
  </el-dialog>
</template>

<script>
  import { checkProject, saveProject } from '@/api/project/project-api'
  import { genUUID } from '@/utils/th_utils.js'
  import { parseTime } from '@/utils/index'
  import { mapActions, mapGetters } from 'vuex'
  import BMapPopup from '@/views/common/BMap/BMapPopup.vue'
  // import baiduMap from '@/views/map/baiduMap'
  // import CommonUploadLargeFileFdfsPopup from '../../system/common/CommonUploadLargeFileFdfsPopup'
  import UploadLargeFileFdfsPopup from '@/views/common/UploadLargeFileFdfsPopup'
  import { getDictList } from '@/api/system/dict-api'
  import tableMix from '@/views/mixins/table'

  export default {
    name: 'TableEdit',
    components: { UploadLargeFileFdfsPopup, BMapPopup },
    mixins: [tableMix],
    props: {},
    data() {
      const validatePojectAccount = (rule, value, callback) => {
        if (!this.formData.isAdd) {
          callback()
          return
        }
        // 校验用户是否存在!
        checkProject({ name: value })
          .then((response) => {
            callback()
          })
          .catch((err) => {
            callback(new Error('项目已存在!'))
          })
      }
      return {
        structureTypeList: [],
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        projectTypeList: [],
        rules: {
          code: [
            { required: true, message: '项目编号为必填', trigger: 'blur' },
            {
              required: true,
              validator: validatePojectAccount,
              trigger: 'blur',
            },
          ],
          name: [
            { required: true, message: '项目名称为必填', trigger: 'blur' },
          ],
          address: [
            { required: true, message: '项目地址必选', trigger: 'blur' },
          ],
          position: [
            { required: true, message: '地理坐标为必选', trigger: 'change' },
          ],
          projectType: [
            { required: true, message: '项目类型为必选', trigger: 'change' },
          ],
          manager: [
            { required: true, message: '安装人员为必填', trigger: 'blur' },
          ],
          ownerName: [
            { required: true, message: '业主单位为必填', trigger: 'blur' },
          ],
          startTime: [
            { required: true, message: '安装时间为必选', trigger: 'change' },
          ],
        },
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
        username: 'user/username',
      }),
    },
    created() {},
    methods: {
      callBackSite(data) {
        this.formData.position = data[0]
        this.$forceUpdate()
      },
      showBMap() {
        this.$refs.BMapPopup.showBMap(this.formData)
      },
      showEdit(row) {
        this.getAllProjectType()
        if (!row) {
          this.title = '新增项目'
          this.initForm()
        } else {
          this.title = '编辑项目'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs.UploadLargeFileFdfsPopup.getParamsData({
            bizId: this.formData.id,
            bizCode: 'projectCommon',
          })
        })
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          code: '',
          name: '',
          projectType: '',
          age: '',
          address: '',
          manager: '',
          position: '',
          startTime: '',
          introduce: '',
          isDaily: 'n',
          ownerName: '',
          alarmType: 'mt',
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveProject(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增项目成功！' : '修改项目成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增项目失败！' : '修改项目失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      // showDepartDialog() {
      //   this.$refs.CommonDepartPopup.showDepartDialog(
      //     this.formData.departList.map((item) => item.id),
      //     true,
      //     false
      //   ) //初始化勾选数据，是否复选，是否要根目录（-1）
      // },
      // callBackDepart(data) {
      //   this.formData.departList = data
      //   this.formData.departNames = data.map((item) => item.label).join(',')
      // },
      // showRoleDialog() {
      //   this.$refs.CommonRolePopup.showRoleDialog(
      //     this.formData.roleList.map((item) => item.id),
      //     true
      //   ) //初始化勾选数据，是否复选
      // },

      getAllProjectType() {
        this.projectTypeList = []
        let param = { dictCode: 'projectType' }
        let projectTypeArray = []
        getDictList(param).then((response) => {
          projectTypeArray = response.result[0].children
          for (const data of projectTypeArray) {
            const treeNode = {}
            treeNode.id = data.dictCode
            treeNode.label = data.dictName
            this.projectTypeList.push(treeNode)
          }
        })
      },
      // callBackRole(data) {
      //   this.formData.roleList = data
      //   this.formData.roleNames = data.map((item) => item.roleName).join(',')
      // },
      // changeAlarmType(val) {
      //   this.formData.alarmType =
      //     this.formData.alarmType === 'at' ? '自动' : '手动'
      // },
      // changeIsDaily(val) {
      //   this.formData.isDayily = this.formData.isDayily === 'y' ? '是' : '否'
      // },
      getFilesInfo(files) {
        // 过滤出图片附件
        const newFiles = files.filter((item) => {
          return /^(jpeg|png|jpg|bmp)$/.test(item.fileExt)
        })
        this.formData.pic = newFiles[newFiles.length - 1].urlPath
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
