<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" style="width:200px;" />
      </el-form-item>
        <el-form-item label="项目权限" prop="projectLists">
          <el-input v-model="formData.projectLists" readonly @click.native="showProjectDialog" type="textarea" :autosize="{ minRows:4, maxRows: 6}" style="width:500px;" />
        </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <CommonProjectPopup ref="CommonProjectPopup" @callBackProject="callBackProject"></CommonProjectPopup>
  </el-dialog>
</template>

<script>
  import {getDataProject,getRelProjectList,getProjectRelsaveData} from '@/api/project/project-api'
  import { genUUID } from '@/utils/th_utils.js'
  import { parseTime } from '@/utils/index'
  import CommonProjectPopup from '../common/CommonProjectPopup'
  import { mapActions, mapGetters } from 'vuex'

  export default {
    name: 'ProjectPermissionsEdit',
    components: {
      CommonProjectPopup
    },
    props:{},
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        projectIds:[],
        relId:'',
        type:''
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
        username: 'user/username',
      }),
    },
    created() {

    },
    methods: {
      async showEdit(row, type) {
        this.title = '绑定权限'
        this.type = type
        this.formData = Object.assign({ projectLists:'' }, row)
        await getRelProjectList({relId:row.id,type: type}).then(response=>{
          this.projectIds = response.result.map(item=>item.projectId)
        })
        await getDataProject({}).then(response=>{
          var projectNames = []
          this.projectIds.forEach(item=>{
            response.result.forEach(val=>{
              if(val.id === item){
                projectNames.push(val.name)
              }
            })
          })
          this.formData.projectLists = projectNames.join(',')
        })
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      callBackProject(data){
        this.formData.projectLists = data.map(item=>item.label).join(',')
        this.projectIds = data.map(item=>item.id)
      },
      showProjectDialog(){
        this.$refs.CommonProjectPopup.showProjectDialog(this.projectIds)
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            var data = {
              selectProjectIds:this.projectIds.join(','),
              relId:this.formData.id,
              type: this.type
            }
            getProjectRelsaveData(data).then(response => {
              this.$baseMessage(
                '绑定权限成功！',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              // console.log(err)
              this.$baseMessage(
                '绑定权限失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
