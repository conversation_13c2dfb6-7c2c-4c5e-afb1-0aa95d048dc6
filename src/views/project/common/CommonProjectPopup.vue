<template>
  <el-dialog
    v-drag
    :title="title"
    :visible.sync="isShowDialog"
    width="500px"
    center
    :close-on-click-modal="false"
    top="5vh"
    append-to-body
  >
    <div class="custom-tree-node" style="margin-left: 163px">
      <el-button type="success" @click="checkAll" style="margin-bottom: 10px">全选</el-button>
      <el-button type="success" @click="closeCheck" style="margin-bottom: 10px">取消全选</el-button>
    </div>
    <el-tree
      ref="tree"
      style="max-height:600px;overflow:auto;"
      :data="treeData"
      node-key="id"
      check-strictly
      show-checkbox
      :props="defaultProps"
      v-loading="treeLoading"
    />
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">
        关闭
      </el-button>
      <el-button type="primary" @click="confirmSelect">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { formatEleProjectTree} from '@/utils/util.js'
import {getDataProject} from '@/api/project/project-api'
export default {
  name: 'CommonProjectPopup',
  components: {
  },
  data() {
    return {
      title: '项目权限绑定',
      isShowDialog: false,
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      treeAllList:[],
      checkedId: '',
      treeLoading: false
    }
  },
  created() {
    this.loadProjectData()
  },
  methods: {
    async loadProjectData() {
      this.treeLoading = true
      await getDataProject({}).then(response => {
        this.treeAllList = response.result
        const json = JSON.parse(JSON.stringify(response.result))
        this.treeData = formatEleProjectTree(json)
      })
      this.treeLoading = false
    },
    showProjectDialog(ids) {
      this.isShowDialog = true
      this.$nextTick(()=>{
        this.$refs.tree.setCheckedKeys(ids)
      })
    },
    //全选
    checkAll() {
      this.flag = true
      this.$nextTick(() => {
        this.$refs.tree.setCheckedNodes(this.treeAllList)
      })
    },
    //取消全选
    closeCheck(){
      this.$nextTick(() => {
        this.$refs.tree.setCheckedNodes([])
      })
    },
    confirmSelect() {
      var selectDatas = []
      selectDatas = this.$refs.tree.getCheckedNodes()
      if(selectDatas.length == 0){
        this.$baseMessage(
          '绑定权限不能为空!' ,
          'error', 'vab-hey-message-error'
        )
        return
      }
      else{
        this.$emit('callBackProject', selectDatas)
      }
      this.isShowDialog = false
    }
  }
}
</script>
