<template>
  <div ref="custom-table" class="custom-table-container" :class="{ 'vab-fullscreen': isFullscreen }">
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form ref="form" :inline="true" label-width="80px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="项目名称" prop="name">
            <el-input v-model="queryForm.name" placeholder="请输入项目名称" />
          </el-form-item>
          <el-form-item label="项目类型" prop="projectType">
            <el-select v-model="queryForm.projectType" placeholder="请选择项目类型" style="width: 200px">
              <el-option v-for="item in projectTypeList" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button icon="el-icon-refresh-right" @click.native="resetForm('form')">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd"
          v-permissions="{ permission: ['projectCommon:add'] }">
          添加
        </el-button>
        <el-button icon="el-icon-delete" type="danger" @click="delBatch"
          v-permissions="{ permission: ['projectCommon:del'] }">
          批删
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button style="margin: 0 10px 10px 0 !important" type="primary" @click="clickFullScreen">
          <vab-icon :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'" />
          表格全屏
        </el-button>
        <el-popover ref="popover" popper-class="custom-table-checkbox" trigger="hover">
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox :disabled="item.disableCheck === true" :label="item.label">
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button icon="el-icon-setting" style="margin: 0 0 10px 0 !important" type="primary">
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table ref="tableSort" v-loading="listLoading" border :data="list" :height="height" :size="lineHeight" stripe
      @selection-change="setSelectRows">
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column v-for="(item, index) in finallyColumns" :key="index" :align="item.center ? item.center : 'center'"
        :label="item.label" :prop="item.prop" :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'">
        <template #default="{ row }">
          <span v-if="item.label === '是否生成日报'">
            <el-tag v-if="row.isDaily === 'y'" type='primary'>是</el-tag>
            <el-tag v-else type='warning'>否</el-tag>
          </span>
          <span v-else-if="item.label === '项目类型'">
            {{ getProjectTypeName(row.projectType) }}
          </span>
          <span v-else-if="item.label === '项目图片'">
           <img :src="row.pic ? row.pic : noImageUrl" :alt="row.name" :title="row.name"  class="preview-image"
                @click="handlePreview(row.pic || noImageUrl,row.name)" >
          </span>
          <span v-else-if="item.label === '预警类型'">
            <el-tag v-if="row.alarmType === 'mt'" type='primary'>手动</el-tag>
            <el-tag v-else type='warning'>自动</el-tag>
          </span>
          <span v-else-if="item.label === '安装时间'">
            {{ dateFormat(row.startTime) }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="280">
        <template #default="{ row }">
          <el-button icon="el-icon-edit" style="margin: 0 10px 0 0 !important" type="primary" @click="handleEdit(row)"
            v-permissions="{ permission: ['projectCommon:edit'] }">
            编辑
          </el-button>
          <el-button icon="el-icon-delete" style="margin: 0 10px 0 0 !important" type="danger" @click="handleDelete(row)"
            v-permissions="{ permission: ['projectCommon:del'] }">
            删除
          </el-button>
          <el-button icon="el-icon-view" style="margin: 0 10px 0 0 !important" type="success" @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
      </template>
    </el-table>
    <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-size="pageInfo.pageSize"
      :total="pageInfo.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
    <table-edit ref="edit" @fetch-data="fetchData" />
    <projectDetail ref="projectDetail" :projectTypeList="projectTypeList" />
    <!-- 放大预览弹窗 -->
    <el-dialog
      :visible.sync="dialogVisible"
      width="30%"
    >
    <div class="image-preview-container">
      <h2 style="text-align: center">{{previewImageTiele}}</h2>
      <img :src="previewImageUrl" :alt="previewImageTiele" class="preview-large-image">
    </div>
    </el-dialog>
  </div>
</template>

<script>
  import TableEdit from './components/ProjectEdit'
  import tableMix from '@/views/mixins/table'
  import { parseTime } from '@/utils'
  import { getDictList } from '@/api/system/dict-api'
  import projectDetail from './components/projectDetail.vue'
  import { getProjectByPage, deleteProject } from '@/api/project/project-api'
  export default {
    name: 'projectCommon',
    components: {
      TableEdit, projectDetail
    },
    mixins: [tableMix],
    data() {
      return {
        dialogVisible: false,// 控制弹窗显示
        previewImageUrl: '',// 当前预览的图片 URL
        previewImageTiele:'',
        noImageUrl: require('@/assets/empty_images/pic_empty.png'),
        checkList: [
          '项目图片',
          '项目编号',
          '项目名称',
          '项目类型',
          '安装时间',
          '是否生成日报',
          '预警类型',
        ],
        columns: [
          {
            label: '项目图片',
            prop: 'pic',
            disableCheck: true,
          },
          {
            label: '项目编号',
            prop: 'code',
            disableCheck: true,
          },
          {
            label: '项目名称',
            prop: 'name',
            disableCheck: true,
          },
          {
            label: '是否生成日报',
            prop: 'isDaily',
          },
          {
            label: '预警类型',
            prop: 'alarmType',
          },
          {
            label: '项目类型',
            prop: 'projectType',
          },
          {
            label: '安装时间',
            prop: 'startTime',
          },
        ],
        queryForm: {
          projectType: '',
          name: '',
          type: '',
        },
        projectTypeList: [],
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
      this.getAllProjectType()
    },
    methods: {
      // 点击图片触发预览
      handlePreview(url,title) {
        this.previewImageUrl = url
        this.previewImageTiele = title
        this.dialogVisible = true
      },
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      backDepart(list) {
        return list.map((item) => item.departName).join(',')
      },
      backRolelist(list) {
        return list.map((item) => item.roleName).join(',')
      },
      handleAdd() {
        this.$refs['edit'].showEdit()
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },
      handleDetail(row) {
        this.$refs['projectDetail'].showDialog(row)
      },
      handleDelete(row) {
        if ({ id: row.id }) {
          this.$baseConfirm('你确定要删除当前项目吗', null, async () => {
            deleteProject({ id: row.id }).then((response) => {
              this.fetchData()
              this.$baseMessage(
                '删除成功!',
                'success',
                'vab-hey-message-success'
              )
            })
          })
        }
      },
      delBatch() {
        if (this.selectRows.length == 0) {
          this.$baseMessage('请最少选中一条记录!', 'error', 'vab-hey-message-error')
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量删除数据吗', null, async () => {
            deleteProject({ id: ids }).then(response => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('批量删除成功！', 'success', 'vab-hey-message-success')
            }).catch(err => {
              this.$baseMessage('批量删除失败！', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      getAllProjectType() {
        getDictList({ dictCode: 'projectType' }).then((res) => {
          const result = res.result[0].children
          this.projectTypeList = result.map(item => {
            return {
              label: item.dictName,
              value: item.dictCode
            }
          })
        })
      },
      getProjectTypeName(ProjectType) {
        if (!ProjectType) return ''
        for (const data of this.projectTypeList) {
          if (data.value == ProjectType) {
            return data.label
          }
        }
      },
      async fetchData() {   //async 异步数据处理方法
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result } = await getProjectByPage(this.queryForm)
        this.list = result.records  //把请求到的数据赋值给表格
        this.pageInfo.total = Number(result.total)
        this.listLoading = false
      },
    },
  }
</script>
<style lang="scss" scoped>
  /* 小图样式 */
  .preview-image {
    width: 120px;
    height: 70px;
    cursor: pointer;
    border-radius: 5px;
    transition: opacity 0.3s;
    &:hover {
      opacity: 0.8;
    }
  }
  /* 大图样式 */
  .preview-large-image {
    max-width: 100%;
    max-height: 80vh;
    display: block;
    margin: 0 auto;
  }

</style>
