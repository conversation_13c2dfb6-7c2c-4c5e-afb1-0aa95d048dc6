<template>
  <div
    class="uploadList-management-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <el-row :gutter="20">
      <el-col :lg="24" :md="24" :sm="24" :xl="24" :xs="24">
        <el-card shadow="hover">
          <vab-query-form>
            <vab-query-form-top-panel>
              <el-form
                ref="form"
                :inline="true"
                label-width="80px"
                :model="queryForm"
                @submit.native.prevent
              >
                <el-form-item label="文件名称" prop="fileName">
                    <el-input v-model="queryForm.fileName" placeholder="请输入名称" />
                </el-form-item>
                <el-form-item>
                  <el-button
                    icon="el-icon-search"
                    native-type="submit"
                    type="primary"
                    @click="handleQuery"
                  >
                    查询
                  </el-button>
                  <el-button icon="el-icon-refresh-right" @click.native="resetForm('form')">重置</el-button>

                </el-form-item>
              </el-form>
            </vab-query-form-top-panel>
            <vab-query-form-left-panel :span="12">
              <el-button icon="el-icon-plus" type="primary" @click="handleAdd">
                新增
              </el-button>
            </vab-query-form-left-panel>
            <vab-query-form-right-panel :span="12">
              <el-button
                style="margin: 0 10px 10px 0 !important"
                type="primary"
                @click="clickFullScreen"
              >
                <vab-icon
                  :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
                />
                表格全屏
              </el-button>
              <el-popover
                ref="popover"
                popper-class="custom-table-checkbox"
                trigger="hover"
              >
                <el-radio-group v-model="lineHeight">
                  <el-radio label="medium">大</el-radio>
                  <el-radio label="small">中</el-radio>
                  <el-radio label="mini">小</el-radio>
                </el-radio-group>
                <template #reference>
                  <el-button style="margin: 0 10px 10px 0 !important" type="primary">
                    <vab-icon icon="line-height" />
                    表格尺寸
                  </el-button>
                </template>
              </el-popover>
              <el-popover popper-class="custom-table-checkbox" trigger="hover">
                <el-checkbox-group v-model="checkList">
                  <vab-draggable v-bind="dragOptions" :list="columns">
                    <div v-for="(item, index) in columns" :key="item + index">
                      <vab-icon icon="drag-drop-line" />
                      <el-checkbox
                        :disabled="item.disableCheck === true"
                        :label="item.label"
                      >
                        {{ item.label }}
                      </el-checkbox>
                    </div>
                  </vab-draggable>
                </el-checkbox-group>
                <template #reference>
                  <el-button
                    icon="el-icon-setting"
                    style="margin: 0 0 10px 0 !important"
                    type="primary"
                  >
                    可拖拽列设置
                  </el-button>
                </template>
              </el-popover>
            </vab-query-form-right-panel>
          </vab-query-form>

          <el-table
            ref="tableSort"
            v-loading="listLoading"
            border
            :data="list"
            :height="height"
            :size="lineHeight"
            stripe
          >

            <el-table-column
              v-for="(item, index) in finallyColumns"
              :key="index"
              :align="item.center ? item.center : 'center'"
              :label="item.label"
              :prop="item.prop"
              :sortable="item.sortable ? item.sortable : false"
              :width="item.width ? item.width : 'auto'"
            >
              <template #default="{ row }">
                <span v-if="item.label === '文件名'">
                  <div v-if="/^(jpeg|png|jpg|bmp)$/.test(row.fileExt)">
                    <el-image
                      title="点击预览"
                      style="width: 80px; height: 80px"
                      :src="fileAddr + row.urlPath"
                      fit="cover"
                      :preview-src-list="[fileAddr + row.urlPath]"
                    />
                    <div>{{ row.fileName }}</div>
                  </div>
                  <!-- <el-link v-else-if="/^(pdf)$/.test(row.fileExt)" type="primary" :href="fileAddr + row.urlPath" target="_blank" :underline="false" title="点击预览">
                    {{ row.fileName }}
                  </el-link> -->
                  <el-link v-else type="primary" :href="'http://kkview.jxth.com.cn:8012/onlinePreview?url='+encodeURIComponent(toBase(fileAddr + row.urlPath))" target="_blank" :underline="false" title="点击预览">
                    {{ row.fileName }}
                  </el-link>
                </span>
                <span v-else-if="item.label === '文件大小(MB)'">
                  {{ fileSizeFormat(row.fileSize) }}
                </span>
                <span v-else-if="item.label === '文件地址'">
                  {{ fileAddr + row.urlPath }}
                </span>
                <span v-else>{{ row[item.prop] }}</span>
              </template>
            </el-table-column>

            <el-table-column
              align="center"
              label="操作"
              width="200"
            >
              <template #default="{ row }">
                <el-button
                  type="success"
                  icon="el-icon-download"
                  @click="downloadFile(row)"
                  style="margin: 0 10px 10px 0 !important"
                >下载</el-button>
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  @click="handleDelete(row)"
                  style="margin: 0 10px 10px 0 !important"
                  v-permissions="{ permission: ['designFile:del'] }"
                >删除
                </el-button>
              </template>
            </el-table-column>
            <template #empty>
              <el-image
                class="vab-data-empty"
                :src="require('@/assets/empty_images/data_empty.png')"
              />
            </template>
          </el-table>
          <el-pagination
            background
            :current-page="pageInfo.curPage"
            :layout="layout"
            :page-size="pageInfo.pageSize"
            :total="pageInfo.total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          />
        </el-card>
      </el-col>
    </el-row>
    <CommonUploadLargeFileFdfsPopup ref="CommonUploadLargeFileFdfsPopup" class="uploadSlot" @refreshUploadFileList="fetchData"/>
  </div>
</template>

<script>
  import {
    getUploadFilesByPage, deleteFile, downLoadFileMinio
  } from '@/api/system/uploadFile-api'
  import tableMix from '@/views/mixins/table'
  import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'
  import { fileAddr } from '@/utils/constants'

  export default {
    name: 'uploadList',
    components: {
      CommonUploadLargeFileFdfsPopup,
    },
    mixins: [tableMix],
    data() {
      return {
        hasCard:true,
        checkList: [
          '文件名', '文件大小(MB)', '文件类型', '上传人','上传时间','文件地址'
          ],
        columns: [
          {
            label: '文件名',
            prop: 'fileName',
            disableCheck: true
          },
          {
            label: '文件大小(MB)',
            prop: 'fileSize',
            disableCheck: true,
          },
          {
            label: '文件类型',
            prop: 'fileExt'
          },
          {
            label: '上传人',
            prop: 'createByName'
          },
          {
            label: '上传时间',
            prop: 'createTime'
          },
          {
            label: '文件地址',
            prop: 'urlPath'
          }
        ],
        queryForm: {
            fileName: '',
            bizId: 'designFile',
            bizCode: 'designFile'
        },
        fileAddr:fileAddr,
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
      projectId() {
            return this.$store.state.acl.projectId;
          }
    },
    created() {
      this.fetchData()
    },
    methods: {
      toBase(url){
        return this.$Base64.encode(url)
      },
      fileSizeFormat(fileSize) {
        const fileSizeMb = parseFloat(fileSize / 1048576).toFixed(4)
        return fileSizeMb
      },
      TreeClick(obj) {
        this.queryForm.bizId = obj.id
        this.queryForm.bizCode = obj.label
        this.pageInfo.curPage = 1
        this.fetchData()
      },
      handleAdd() {
        this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog(this.queryForm)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前文件吗', null, async () => {
            deleteFile(row.id).then(response => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success')
            }).catch(err=>{
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      downloadFile(row) {
        downLoadFileMinio(row.id).then(response => {
          const data = response
          if (!data) {
            return
          }
          // 构造a标签 通过a标签来下载
          const url = window.URL.createObjectURL(new Blob([data]))
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = url
          // 此处的download是a标签的内容，固定写法，不是后台api接口
          a.setAttribute('download', row.fileName + '.' + row.fileExt)
          document.body.appendChild(a)
          // 点击下载
          a.click()
          // 下载完成移除元素
          document.body.removeChild(a)
          // 释放掉blob对象
          window.URL.revokeObjectURL(url)
        })
      },
      async fetchData() {
        const curPage = this.pageInfo.curPage
        const pageSize = this.pageInfo.pageSize
        this.queryForm.bizId = this.projectId;
        this.listLoading = true
        const { result: { records, total }} = await getUploadFilesByPage({...this.queryForm,curPage, pageSize,})

        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  $base: '.uploadList-management';
  #{$base}-container {
    padding: 0 !important;
    background: $base-color-background !important;

    &.vab-fullscreen{
      padding: 20px !important;
    }
  }
</style>
