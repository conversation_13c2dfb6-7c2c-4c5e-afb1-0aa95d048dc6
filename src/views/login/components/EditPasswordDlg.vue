<template>
  <el-dialog
    :title="title"
    :visible.sync="isShowLogsDialog"
    width="380px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form ref="passwordForm" :model="dataForm" :rules="rules" label-width="80px">
      <el-form-item label="手机号" prop="telephone">
        <el-input  v-model="dataForm.telephone" maxlength="11" />
      </el-form-item>
      <el-form-item label="验证码" prop="checkCode">
        <div style="display:flex;">
            <el-input v-model="dataForm.checkCode" />
            <el-button type="primary" style="margin-left:10px;width:120px"
            :disabled="times>0 || dataForm.telephone==''"
            @click="getTelephoneCode"
            >
              <template v-if="times>0">
                  {{60-times}}S
              </template>
              <template v-else>发送验证码</template>
            </el-button>
        </div>
        
      </el-form-item>
      <el-form-item label="新密码" style="height: 50px;" prop="newPassword" 
      v-if="dataForm.checkCode!=''">
        <el-input v-model="dataForm.newPassword"  />
      </el-form-item>
      <el-form-item label="再次输入" prop="newPasswords"
      v-if="dataForm.checkCode!=''">
        <el-input v-model="dataForm.newPasswords"  />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">
        取消
      </el-button>
      <el-button type="primary" @click="editPassword">
        提交
      </el-button>
    </template>
  </el-dialog>
</template>

<script>
import { 
    updatePassword, checkPassword,
    getTelephoneCode,checkCode,editPassword
} from '@/api/user'
import { toLoginRoute } from '@/utils/routes'

export default {
  name: 'EditPasswordDlg',
  data() {
    const validateNewPassword = (rule, value, callback) => {
      const pattern = /^(?![A-Za-z]+$)(?![A-Z\d]+$)(?![A-Z\W]+$)(?![a-z\d]+$)(?![a-z\W]+$)(?![\d\W]+$)\S{8,}$/
      if (value && !pattern.test(value)) {
        callback(new Error('请输入8位以上大小写英文字母、数字或者符号，且大小写英文字母、数字和标点符号至少包含三种'))
        return
      }
      const newPassword = this.dataForm.newPassword
      const newPasswords = this.dataForm.newPasswords
      if(rule.field === "newPassword") return callback()
      if (newPassword !== newPasswords) {
        callback(new Error('两次密码输入不一致!'))
      } else {
        callback()
      }
    }
    const validatePhone = (rule, value, callback) => {
      let pattern = /^1(3|4|5|7|8|6|9)\d{9}$/
      if (!pattern.test(value)) {
        callback(new Error('请输入正确的手机号!'))
      } else {
        callback()
      }
    }
    return {
      title: '忘记密码',
      isShowLogsDialog: false,
      dataForm: {
        telephone: '',
        checkCode: '',
        newPassword: '',
        newPasswords: ''
      },
      rules: {
        telephone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        checkCode: [
          { required: true, message: '请输入验证码', trigger: 'blur' },
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' }
        ],
        newPasswords: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateNewPassword, trigger: 'blur' }
        ]
      },
      times:0,
      timer:null
    }
  },
  destroyed() {
    this.clearTimer()
  },
  methods: {
    showEditPassword(data) {
      this.dataForm.userName = data.userName
      this.isShowLogsDialog = true
    },
    async logout() {
      if (!this.$store.getters['user/token']) {
        return
      }
      await this.$store.dispatch('user/logout')
      await this.$router.push(toLoginRoute(this.$route.fullPath))
    },
    editPassword() {
      this.$refs['passwordForm'].validate(async (valid) => {
        if (valid) {
          checkCode({
            telephone:this.dataForm.telephone,
            checkCode:this.dataForm.checkCode,
          }).then(response=>{
              editPassword({
                  telephone:this.dataForm.telephone,
                  newPassword:this.dataForm.newPassword,
              }).then(response=>{
                  this.$baseMessage(response.msg, 'success', 'vab-hey-message-success')
                  this.close()
                  this.logout()
              }).catch(err=>{
                this.$baseMessage(err.msg, 'error', 'vab-hey-message-error')
              })
              
          }).catch(err=>{
            this.$baseMessage('验证码校验失败！', 'error', 'vab-hey-message-error')
          })
          
        }
      })
    },
    close() {
      this.$refs['passwordForm'].resetFields()
      this.dataForm = this.$options.data().dataForm
      this.isShowLogsDialog = false
    },
    async getTelephoneCode(){
        getTelephoneCode({
            telephone:this.dataForm.telephone
        }).then(respnse=>{
            this.$baseMessage('验证码发送成功!', 'success', 'vab-hey-message-success')
            this.timer = setInterval(() => {
                if(this.times<60){
                  this.times++
                }else{
                  this.clearTimer()
                }
            }, 1000)
        }).catch(err=>{
          this.$baseMessage(err.msg, 'error', 'vab-hey-message-error')
        })
        
    },
    clearTimer(){
        if(this.timer!=null){
            clearInterval(this.timer)
            this.timer=null
            this.times=0
        }
    }
  }
}
</script>
