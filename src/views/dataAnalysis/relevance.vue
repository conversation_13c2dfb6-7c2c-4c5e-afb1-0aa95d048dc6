<template>
  <div class="card-container">
    <el-card shadow="hover">
      <template>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
          <el-select v-model="projectId" filterable size="medium" @change="getDeviceTypeByProjectId">
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
          <el-form-item label="关联监测项" label-width="120px">
            <el-select
              v-model="typers"
              class="date-item"
              filterable
              collapse-tags
              placeholder=""
              @change="changeSelect"
            >
              <el-option
                v-for="item in deviceTypes"
                :key="item.type"
                :label="item.typeName"
                :value="item.type"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="测点编号">
            <el-select
              v-model="deviceIds"
              class="date-item"
              filterable
              multiple
              collapse-tags
              placeholder=""
            >
              <el-option
                v-for="item in deviceList"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="isShow" label="方向">
            <el-select
              v-model="direction"
              class="date-item"
              filterable
              collapse-tags
              placeholder=""
            >
              <el-option
                v-for="item in directionList"
                :key="item.value"
                :label="item.value"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="isHumidShow" label="温湿度">
            <el-select
              v-model="humid"
              class="date-item"
              filterable
              collapse-tags
              placeholder=""
            >
              <el-option
                v-for="item in humidList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          </br>
          <el-form-item label="被关联监测项" label-width="120px">
            <el-select
              v-model="relevanceType"
              class="date-item"
              filterable
              collapse-tags
              placeholder=""
              @change="changeSelectes"
            >
              <el-option
                v-for="item in deviceTypes"
                :key="item.type"
                :label="item.typeName"
                :value="item.type"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="测点编号">
            <el-select
              v-model="relevanceIds"
              class="date-item"
              filterable
              multiple
              collapse-tags
              placeholder=""
            >
              <el-option
                v-for="item in relevanceList"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="isShows" label="方向">
            <el-select
              v-model="directions"
              class="date-item"
              filterable
              collapse-tags
              placeholder=""
            >
              <el-option
                v-for="item in directionLists"
                :key="item.value"
                :label="item.value"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item v-show="isHumidShows" label="温湿度">
            <el-select
              v-model="humids"
              class="date-item"
              filterable
              collapse-tags
              placeholder=""
            >
              <el-option
                v-for="item in humidList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="时间选择">
              <el-date-picker
                v-model="receiveTime"
                :default-time="['00:00:00', '23:59:59']"
                type="daterange"
                range-separator=":"
                size="small"
                class="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                class="filter-item"
                type="primary"
                icon="el-icon-search"
                @click="reflushData()"
                >查询</el-button
              >
            </el-form-item>
          </el-form>
      </template>
    </el-card>
    <el-row :gutter="10">
      <el-col :span="12">
        <el-card v-loading="lineLoading" class="box-card" shadow="hover">
          <div slot="header" class="clearfix" style="text-align: center">
            <span class="role-span">{{ title1 }}</span>
          </div>
          <data-chart
            style="height: calc(100vh - 420px);width: 100%"
            :chart-data="chartData"
            :chart-info="chartInfoS"
            :chart-info-w="chartInfoW"
            :unit1="unitx1"
            :unit2="unity1"
          />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card v-loading="lineLoading" class="box-card" shadow="hover">
          <div slot="header" class="clearfix" style="text-align: center">
            <span class="role-span">{{ title2 }}</span>
          </div>
          <line-chart
            style="height: calc(100vh - 420px);width: 100%"
            :chart-data="chartData"
            :chart-info="chartInfoS"
            :chart-info-w="chartInfoW"
            :unit1="unitName1"
            :unit2="unitName2"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>

import dataChart from "./dataChart";
import lineChart from "./lineChart";
import { getDeviceTypeByProjectId } from '@/api/online/online-api'
import { getDataDeviceManagement } from '@/api/device/deviceManagement-api'
import {
  getJoinDeviceData,
  getJoinDevice
} from "@/api/dataAnalysis/dataAnalysis-api";
import {getProjectListWithAuth} from "@/api/project/project-api";

export default {
  components: { dataChart,lineChart },
  dicts: ["device_type"],
  data() {
    return {
      directions: "",
      direction: "",
      humid: '', // 温湿度
      humids: '', // 温湿度
      directionLists: [],
      isShows: false,
      directionList: [],
      humidList: [{label: '温度',value: 'w'},{label: '湿度',value: 's'}], // 温湿度参数值列表
      relevanceIds: "",
      relevanceList: [],
      relevanceType: "",
      checked: false,
      lineLoading: false,
      type: "",
      typers: "",
      title1: "关联分析效果图",
      title2: "归一化关联分析效果图",
      unitx1: "",
      unity1: "",
      unitName1: '', // 归一化关联分析图Y轴名称1
      unitName2: '',  // 归一化关联分析图Y轴名称2
      pointTitle: "测点图展示",
      deviceIds: [],
      deviceId: "",
      receiveTime: "",
      deviceList: [],
      deviceTypeList: [],
      isShow: false,
      isHumidShow: false, // 关联监测项温湿度选择框显示
      isHumidShows: false, // 被关联监测项温湿度选择框显示
      chartData: {
        dataX: [],
        dataY: {},
        legend: []
      },
      chartData1: {
        dataX1: [],
        dataY1: {},
        legend1: []
      },
      projectId: '',
      deviceTypes: [],
      chartInfoW: {
        maxVal: "",
        minVal: "",
        alarmDown1: "",
        alarmUp1: "",
        alarmDown2: "",
        alarmUp2: "",
        alarmDown3: "",
        alarmUp3: "",
        alarmDown4: "",
        alarmUp4: ""
      },
      chartInfoI: {
        maxVal: "",
        minVal: "",
        alarmDown1: "",
        alarmUp1: "",
        alarmDown2: "",
        alarmUp2: "",
        alarmDown3: "",
        alarmUp3: "",
        alarmDown4: "",
        alarmUp4: ""
      },
      chartInfoP: {
        maxVal: "",
        minVal: "",
        alarmDown1: "",
        alarmUp1: "",
        alarmDown2: "",
        alarmUp2: "",
        alarmDown3: "",
        alarmUp3: "",
        alarmDown4: "",
        alarmUp4: ""
      },
      unit: "unit",
      chartInfoS: {
        maxVal: "",
        minVal: "",
        alarmDown1: "",
        alarmUp1: "",
        alarmDown2: "",
        alarmUp2: "",
        alarmDown3: "",
        alarmUp3: "",
        alarmDown4: "",
        alarmUp4: ""
      },
      projectList: []
    };
  },
  created() {
    this.getProjectList()
  },
  methods: {
    getProjectList() {
      getProjectListWithAuth({}).then((res) => {
        this.projectList = res.result || []
        this.projectId = this.projectList[0].id
        this.getDeviceTypeByProjectId();
      })
    },
    getDeviceShow(type) {
      if (
        type === "incline" ||
        type === "floord" ||
        type === "survey" ||
        type === "gnss"
      ) {
        return true;
      }
      return false;
    },
    getDeviceDirection(params, type) {
      if (type === "incline" || type === "floord" || type === "survey") {
        params = [];
        params.push({ value: "x" });
        params.push({ value: "y" });
        return params;
      } else if (type === "gnss") {
        params = [];
        params.push({ value: "x" });
        params.push({ value: "y" });
        params.push({ value: "z" });
        return params;
      }
    },
    // 解析设备类型 [3,3,3,1,2,1,1,2,3,3,4]
    // parseType(value) {
    //   let dev = this.dict.label.device_type[value];
    //   const projectId = sessionStorage.getItem('def_project_Id')
    //   if (dev === "静力水准") {
    //     dev = "挠度";
    //   } else if (dev === "振动加速度") {
    //     dev = "振动";
    //   } else if (dev === "倾角仪") {
    //     dev = "倾斜";
    //   } else if (projectId === "4" && dev === "伸缩缝") {
    //     dev = "主梁位移";
    //   }
    //   return dev;
    // },
    // 获取桥对应的所有设备类型
    getDeviceTypeByProjectId() {
      getDeviceTypeByProjectId({projectId: this.projectId})
        .then(res => {
          this.deviceTypes = res.result.filter(item => {
            // 过滤：风速风向、振动
            return item.type != 'vibrate' && item.type != 'wind'
          })
        })
        .catch(() => {});
    },
    changeSelect() {
      this.direction = "";
      this.getDevices();
      // this.getJoinType();
      this.deviceIds = [];
      this.isShow = this.getDeviceShow(this.typers);
      this.isHumidShow = this.typers == 'humid' || this.typers == 'soil_humid' ? true : false
      this.directionList = this.getDeviceDirection(
        this.directionList,
        this.typers
      );
    },
    changeSelectes() {
      this.directions = "";
      this.relevanceIds = [];
      this.getJoinDevices();
      this.isShows = this.getDeviceShow(this.relevanceType);
      this.isHumidShows = this.relevanceType == 'humid' || this.relevanceType == 'soil_humid' ? true : false
      this.directionLists = this.getDeviceDirection(
        this.directionLists,
        this.relevanceType
      );
    },
    // async getJoinType() {
    //   const params = {};
    //   params.projectId = sessionStorage.getItem('def_project_Id')
    //   params.projectType = sessionStorage.getItem('def_project_type')
    //   if (this.typers === "humid_w") {
    //     params.type = "humid";
    //   } else {
    //     params.type = this.typers;
    //   }
    //   getJoinDevice(params)
    //     .then(res => {
    //       this.deviceTypeList = res;
    //     })
    //     .catch(() => {});
    // },
    // 获取桥对应的下拉设备数据
    getDevices() {
      const params = {};
      params.projectId = this.projectId
      params.type = this.typers;
      getDataDeviceManagement(params)
        .then(res => {
          this.deviceList = res.result;
         /* if (this.deviceList != null && this.deviceList.length > 0) {
            //this.deviceIds[0] = this.deviceList[0].id;
            // this.reflushData()
          }
         /!* let str = {};
          str = this.deviceList.find(item => {
            if (item.id === this.deviceIds) {
              return item.name;
            }
          });
          console.info(str)
          this.deviceId = str.name;*!/*/
        })
        .catch(() => {});
    },
    getJoinDevices() {
      const params = {};
      params.projectId = sessionStorage.getItem('def_project_Id')
      params.type = this.relevanceType;
      getDataDeviceManagement(params)
        .then(res => {
          this.relevanceList = res.result;
          /*if (this.relevanceList != null && this.relevanceList.length > 0) {
            this.relevanceIds[0] = this.relevanceList[0].id;
            // this.reflushData()
          }*/
        })
        .catch(() => {});
    },
    // 刷新数据
    reflushData() {
      this.$nextTick(() => {
        this.getJoinChartData();
      });
      this.unitx1 = this.setChartTite(this.typers,this.humid);
      this.unity1 = this.setChartTite(this.relevanceType,this.humids);
      this.unitName1 = this.formatTypers(this.typers,this.humid)
      this.unitName2 = this.formatTypers(this.relevanceType,this.humids)
    },
    setChartTite(type,humid) {
      if (type === "strain") {
        return "应变值（με）";
      } if (type === "optical_strain") {
        return "应变值（με）";
      } else if (type=='humid'&&humid=='w') {
        return "温度（°C）";
      } else if (type=='humid'&&humid=='s') {
        return "相对湿度（RH%）";
      }  else if (type=='soil_humid'&&humid=='w') {
        return "温度（°C）";
      } else if (type=='soil_humid'&&humid=='s') {
        return "相对湿度（RH%）";
      } else if (type === "floor") {
        return "沉降值(mm)";
      } else if (type === "displacement" || type === 'bp_displacement') {
        return "位移值(mm)";
      } else if (type === "zl_displacement") {
        return "位移值(mm)";
      } else if (type === "optical_strain") {
        return "应变值（με）";
      } else if (type === "rifts") {
        return "裂缝值(mm)";
      } else if (type === "soly") {
        return "索力(KN)";
      } else if (type === "m_soly") {
        return "锚索力(KN)";
      } else if (type === "incline") {
        return "角度（°）";
      } else if (type === "gnss") {
        return "位移值(mm)";
      } else if (type === "wind") {
        return "风速m/s";
      } else if (type === "floord") {
        return "挠度(mm)";
      } else if (type === "survey") {
        return "偏移量(mm)";
      } else if (type === "brilevel") {
        return "偏移量(mm)";
      } else if (type === "water") {
        return "水位(m)";
      }  else if (type === "pressure") {
        return "水压力(Kpa)";
      } else if (type === "rainfall") {
        return "降雨量(mm)";
      } else if (type === "strain_d") {
        return "应变值（με）";
      }
    },
    // 格式化关联监测项名称
    formatTypers(type,humid) {
      const data = this.deviceTypes.find(item => {
        return item.type == type
      })
      if (type=='humid'&&humid=='w') {
        return "温度";
      } else if (type=='humid'&&humid=='s') {
        return "湿度";
        } else if (type=='soil_humid'&&humid=='w') {
        return "温度";
        } else if (type=='soil_humid'&&humid=='s') {
        return "湿度";
        }
      return data.typeName
    },
    // 格式化被关联监测项名称
    // formatRelevanceType(type) {
    //   const data = this.deviceTypes.find(item => {
    //     return item.type == type
    //   })
    //   return data.typeName
    // },
    // 获取图表数据
    getJoinChartData() {
      const params = {};
      params.deviceIds = this.deviceIds;
      if (this.direction !== "" && this.direction.length !== "0") {
        params.type = this.typers + "_" + this.direction;
      } else if ((this.typers === "humid" || this.typers === "soil_humid") && this.humid) {
        params.type = this.typers + '_' + this.humid;
      } else {
        params.type = this.typers;
      }
      if (this.directions !== "" && this.directions.length !== "0") {
        params.relevanceType = this.relevanceType + "_" + this.directions;
      } else if ((this.relevanceType === "humid" || this.relevanceType === "soil_humid") && this.humids) {
        params.relevanceType = this.relevanceType + '_' + this.humids;
      } else {
        params.relevanceType = this.relevanceType;
      }
      if (this.typers === "humid_w") {
        this.typers === "humid";
      }
      params.relevanceIds = this.relevanceIds;
      params.projectId = sessionStorage.getItem('def_project_Id')
      // params = timeQuery(params, this.timeQ, this.receiveTime)
      params.startTime = this.receiveTime[0];
      params.endTime = this.receiveTime[1];
      this.lineLoading = true;
      if (this.lineLoading) {
        getJoinDeviceData(params)
          .then(res => {
            this.lineLoading = false;
            this.chartData = res.result;
          })
          .catch(() => {
            this.lineLoading = false;
          });
      }
    },
    getDevice(deviceId) {
      const dev = this.deviceList.find(device => {
        if (device.id === deviceId) {
          return device;
        }
      });
      return dev;
    }
  }
};
</script>
<style lang="scss" scoped>
.centerForm {
  padding: 0  300px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
.card-container {
  padding:  0 !important;
  background: $base-color-background !important;
}
.box-card {
  margin-bottom: 0 !important;
}
::v-deep .el-card__body {
  padding: 20px 10px !important;
}
</style>
