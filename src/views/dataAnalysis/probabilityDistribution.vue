<template>
  <el-col :lg="128" :md="132" :sm="144" :xl="126" :xs="144">
    <el-card shadow="hover">
      <template #header>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
            <el-select v-model="queryForm.projectId" filterable size="medium" @change="getDeviceType">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="postInfo-container-item"
            label="监测项:"
            label-width="66px"
          >
            <el-select
              ref="optionRef"
              v-model="queryForm.type"
              placeholder="选择监测项"
              style="width: 150px"
              @change="onchange"
            >
              <el-option
                v-for="item in selectDeviceType"
                :key="item.type"
                :label="item.typeName"
                :value="item.type"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="测点编号">
            <el-select
              v-model="queryForm.deviceId"
              class="date-item"
              style="width: 150px"
              collapse-tags
              filterable
              placeholder=""
            >
              <el-option
                v-for="item in deviceList"
                :key="item.name"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item v-show="showDirection" label="方向" label-width="52px">
            <el-select v-model="queryForm.direction" placeholder="" style="width: 100px">
              <el-option
                v-for="item in directionList"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="时间" prop="queryDate" label-width="52px">
            <el-date-picker
              v-model="queryForm.queryDate"
              end-placeholder="结束时间"
              start-placeholder="开始时间"
              style="width: 370px"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>

      <vab-chart
        :init-options="initOptions"
        :option="option"
        style="height: 800px; width: 100%;padding: 30px 30px 0px"
        theme="vab-echarts-theme"
      />
      <div
        style="
          font-size: 15px;
          position: absolute;
          bottom: 500px;
          left: 55px;
          text-align: center;
          transform: rotate(270deg);
          transform-origin: left;
        "
      >
        {{ titleUnitYaxisInfo.yaxis }}
      </div>
      <div style="color: #303133; font-size: 15px; text-align: center">
        正态分位数
      </div>
    </el-card>
  </el-col>
</template>

<script>
  import VabChart from '@/extra/VabChart'
  import { getDeviceTypeByProjectId } from '@/api/online/online-api'
  import { getDataDeviceManagement } from '@/api/device/deviceManagement-api'
  import { getProbabilityDistributionList } from '@/api/dataAnalysis/probabilityDistribution-api'
  import { getDeviceTitleAndUnit } from '@/api/online/online-api'
  import { parseTime, formatTimeByDay } from '@/utils'
  import {getProjectListWithAuth} from "@/api/project/project-api";

  export default {
    name: 'VabChartScatter',
    components: {
      VabChart,
    },
    data() {
      return {
        deviceList: [],
        selectDeviceType: [],
        directionList: [],
        type: '',
        typeName: '',
        showDirection: false,
        titleUnitYaxisInfo: {},
        queryForm: {
          projectId: '',
          type: '',
          direction: '', //方向
          deviceId: [],
          queryDate: [],
          queryDateStart: null,
          queryDateEnd: null,
        },
        initOptions: {
          renderer: 'svg',
        },
        chartData: [[0, 0]],
        chartData2: [[0, 0]],
        projectList: []
      }
    },
    computed: {
      option() {
        return {
          grid: {
            top: 40,
            right: 20,
            bottom: 40,
            left: 40,
          },
          dataset: [
            {
              source: this.chartData,
            },
            {
              source: this.chartData2,
            },
            {
              transform: {
                type: 'ecStat:regression',
                config: { method: 'polynomial', order: 3 },
              },
            },
          ],
          title: {
            text: '概率分析趋势图',
            left: 'center',
            top: 0,
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
          },
          xAxis: {
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
            splitNumber: 20,
          },
          yAxis: {
            // min: -40,
            min: function(value) {//取最小值向下取整为最小刻度
             if(value.min === value.max){
                return value.min - 0.01
              }else {
                return Math.floor(value.min * 100)/100
              }
            },
            max: function(value) {//取最大值向上取整为最大刻度
             if(value.min === value.max){
                return value.max + 0.01
              }else {
                return Math.ceil(value.max * 100)/100
              }

            },
            splitLine: {
              lineStyle: {
                type: 'dashed',
              },
            },
          },
          series: [
            {
              symbolSize: 10,
              type: 'scatter',
              name: '实际值',
            },
            {
              name: '最佳拟合直线',
              type: 'line',
              smooth: true,
              datasetIndex: 1,
              symbolSize: 0.1,
              symbol: 'circle',
              // label: { show: true, fontSize: 16 },
              labelLayout: { dx: -20 },
              encode: { label: 2, tooltip: 1 },
            },
          ],
        }
      },
    },
    mounted() {
      this.getProjectList()

      //默认最近7天
      let date = parseTime(new Date(), '{y}-{m}-{d}')
      this.queryForm.queryDate = [
        formatTimeByDay(date, -7) + ' 00:00:00',
        date + ' 23:59:59',
      ]
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
          this.queryForm.projectId = this.projectList[0].id
          this.getDeviceType()
        })
      },
      handleQuery() {
        this.fetchData()
      },
      resetForm(formName) {
        this.$refs[formName].resetFields()
        this.fetchData()
      },
      async fetchData() {
        if (this.queryForm.queryDate) {
          this.queryForm.queryDateStart = this.queryForm.queryDate[0]
          this.queryForm.queryDateEnd = this.queryForm.queryDate[1]
        }

        this.listLoading = true
        const res = await getProbabilityDistributionList(this.queryForm)
        this.chartData = res.result.dataList1
        this.chartData2 = res.result.dataList2
        this.directionList = res.result.directionList
        this.getTitleAndUnit()
        this.listLoading = false
      },
      async getDeviceType() {
        await getDeviceTypeByProjectId({
          projectId: this.queryForm.projectId,
          isBestFit: 'Y',
        })
          .then((res) => {
            this.selectDeviceType = res.result
            this.type = this.selectDeviceType[0].type
            this.queryForm.type = this.type
            this.onchange(this.type)
          })
          .catch(() => {})
        await this.getDeviceList(this.type)
      },
      //设备位置
      async getDeviceList(row) {
        const data = {
          projectId: this.queryForm.projectId,
          type: row,
        }
        await getDataDeviceManagement(data)
          .then((res) => {
            this.deviceList = res.result
            this.queryForm.deviceId = this.deviceList[0].id
          })
          .catch(() => {})
        await this.fetchData()
      },
      async getTitleAndUnit() {
        let type = this.queryForm.type
        if(this.queryForm.direction !== ''){
          type = this.queryForm.type + '_' + this.queryForm.direction
        }
        await getDeviceTitleAndUnit({
          projectId: this.queryForm.projectId,
          type: type,
        })
          .then((res) => {
            this.titleUnitYaxisInfo = res.result
            if (res.result.yaxis) {
              let str = res.result.yaxis.split(/,|，|\s+/)
              this.titleUnitYaxisInfo.yaxis = str[0]
            }
          })
          .catch(() => {})
      },
      onchange(value) {
        console.log(value)
        if (value === 'floor_d' || value === 'incline' || value === 'survey') {
          this.showDirection = true
          this.directionList = ['x', 'y']
          this.queryForm.direction = 'x'
        } else if (value === 'gnss') {
          this.showDirection = true
          this.directionList = ['x', 'y', 'z']
          this.queryForm.direction = 'x'
        } else if (value.includes('humid')) {
          this.showDirection = true
          this.directionList = ['s', 'w']
          this.queryForm.direction = 's'
        } else if (value === 'wind') {
          this.showDirection = true
          this.directionList = ['s', 'd']
          this.queryForm.direction = 's'
        } else {
          this.showDirection = false
          this.queryForm.direction = ''
        }
        this.getTitleAndUnit()
        //获取相应的设备
        this.getDeviceList(value)
      },
    },
  }
</script>
<style lang="scss" scoped>
.centerForm {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
}
</style>
