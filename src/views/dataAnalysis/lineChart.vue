<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "lineChart",
  props: {
    className: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "350px"
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
    chartInfo: {
      type: Object,
      required: true
    },
    chartInfoW: {
      type: Object,
      required: true
    },
    unit1: {
      type: String,
      required: true
    },
    unit2: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    chartData: {
      deep: true,
      handler() {
        this.setOptions();
      }
    },
    chartInfo: function () {
      this.setOptions();
    },
    chartInfoW: function () {
      if (JSON.stringify(this.chartInfoW) !== "{}") {
        this.setOptions();
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
      window.addEventListener('resize', () => {
        this.chart.resize();
      });
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, "macarons");
      this.setOptions();
    },
    setOptions() {
      // 组装警戒
      // const markArea = this.markArea()
      // 组装series
      const series = [];
      if(this.chartData.dataz) {
        var map = new Map(Object.entries(this.chartData.dataz));
      }
      for (let i = 0; i < this.chartData.legend.length; i++) {
        const name = this.chartData.legend[i];
        let yIndex = 0;
        if (i < 1) {
          yIndex = 0;
        } else {
          yIndex = 1;
        }
        const sere = {
          name: name,
          data: map.get(name),
          type: "line",
          connectNulls: true,
          smooth: true,
          symbol: "none",
          yAxisIndex: yIndex,
          animationDuration: 2800,
          animationEasing: "cubicInOut"
          // markArea: markArea
        };
        series.push(sere);
        // console.info(JSON.stringify(this.chartData.dataX))
        // console.info(JSON.stringify(series))
      }
      // 图表
      this.chart.setOption(
        {
          legend: {
            data: this.chartData.legend,
            orient: "vertical", // 垂直显示
            y: "center", // 延Y轴居中
            x: "right" // 居右显示
          },
          toolbox: {
            show: true,
            right: 0,
            top: 0,
            feature: {
              saveAsImage: {
                title: '',
                name: '归一化关联分析效果图',
                type: 'png'
              }
            }
          },
          xAxis: {
            type: "category",
            data: this.chartData.datax
          },
          yAxis: [
            {
              type: "value",
              name: this.unit1,
              scale: true
            },
            {
              type: "value",
              name: this.unit2,
              scale: true
            }
          ],
          series: series,
          grid: {
            left: 30,
            right: '15%',
            bottom: 10,
            top: 30,
            containLabel: true
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "cross"
            },
            padding: [5, 10]
          }
        },
        true
      );
    },
    // 组装警戒
    markArea() {
      const data = [];
      if (
        this.chartInfo.alarmUp1 !== null &&
        this.chartInfo.alarmUp1 !== "" &&
        this.chartInfo.alarmDown1 !== null &&
        this.chartInfo.alarmDown1 !== ""
      ) {
        data.push([
          {
            yAxis: this.chartInfo.alarmDown1,
            itemStyle: {
              color: "rgb(198,22,40)"
            }
          },
          {
            yAxis: this.chartInfo.alarmUp1
          }
        ]);
      }
      if (
        this.chartInfo.alarmUp2 !== null &&
        this.chartInfo.alarmUp2 !== "" &&
        this.chartInfo.alarmDown2 !== null &&
        this.chartInfo.alarmDown2 !== ""
      ) {
        data.push([
          {
            yAxis: this.chartInfo.alarmDown2,
            itemStyle: {
              color: "rgb(198,93,6)"
            }
          },
          {
            yAxis: this.chartInfo.alarmUp2
          }
        ]);
      }
      if (
        this.chartInfo.alarmUp3 !== null &&
        this.chartInfo.alarmUp3 !== "" &&
        this.chartInfo.alarmDown3 !== null &&
        this.chartInfo.alarmDown3 !== ""
      ) {
        data.push([
          {
            yAxis: this.chartInfo.alarmDown3,
            itemStyle: {
              color: "rgb(198,195,23)"
            }
          },
          {
            yAxis: this.chartInfo.alarmUp3
          }
        ]);
      }
      if (
        this.chartInfo.alarmUp4 !== null &&
        this.chartInfo.alarmUp4 !== "" &&
        this.chartInfo.alarmDown4 !== null &&
        this.chartInfo.alarmDown4 !== ""
      ) {
        data.push([
          {
            yAxis: this.chartInfo.alarmDown4,
            itemStyle: {
              color: "rgb(116,195,198)"
            }
          },
          {
            yAxis: this.chartInfo.alarmUp4
          }
        ]);
      }
      // 温度
      if (JSON.stringify(this.chartInfoW) !== "{}") {
        if (
          this.chartInfoW.alarmUp1 !== null &&
          this.chartInfoW.alarmUp1 !== "" &&
          this.chartInfoW.alarmDown1 !== null &&
          this.chartInfoW.alarmDown1 !== ""
        ) {
          data.push([
            {
              yAxis: this.chartInfoW.alarmDown1,
              itemStyle: {
                color: "rgb(198,22,40)"
              }
            },
            {
              yAxis: this.chartInfoW.alarmUp1
            }
          ]);
        }
        if (
          this.chartInfoW.alarmUp2 !== null &&
          this.chartInfoW.alarmUp2 !== "" &&
          this.chartInfoW.alarmDown2 !== null &&
          this.chartInfoW.alarmDown2 !== ""
        ) {
          data.push([
            {
              yAxis: this.chartInfoW.alarmDown2,
              itemStyle: {
                color: "rgb(198,93,6)"
              }
            },
            {
              yAxis: this.chartInfoW.alarmUp2
            }
          ]);
        }
        if (
          this.chartInfoW.alarmUp3 !== null &&
          this.chartInfoW.alarmUp3 !== "" &&
          this.chartInfoW.alarmDown3 !== null &&
          this.chartInfoW.alarmDown3 !== ""
        ) {
          data.push([
            {
              yAxis: this.chartInfoW.alarmDown3,
              itemStyle: {
                color: "rgb(198,195,23)"
              }
            },
            {
              yAxis: this.chartInfoW.alarmUp3
            }
          ]);
        }
        if (
          this.chartInfoW.alarmUp4 !== null &&
          this.chartInfoW.alarmUp4 !== "" &&
          this.chartInfoW.alarmDown4 !== null &&
          this.chartInfoW.alarmDown4 !== ""
        ) {
          data.push([
            {
              yAxis: this.chartInfoW.alarmDown4,
              itemStyle: {
                color: "rgb(116,195,198)"
              }
            },
            {
              yAxis: this.chartInfoW.alarmUp4
            }
          ]);
        }
      }
      const markArea = {
        data: data
      };
      return markArea;
    }
  }
}
</script>

<style scoped>

</style>
