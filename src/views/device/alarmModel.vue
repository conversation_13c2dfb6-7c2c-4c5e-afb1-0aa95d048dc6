<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
            <el-select v-model="queryForm.projectId" filterable size="medium" @change="getDeviceType">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label-width="80px" label="监测项:" class="postInfo-container-item" prop="type">
            <el-select v-model="queryForm.type" placeholder="选择监测项" style="width:200px;">
              <el-option v-for="(item, i) in selectDeviceType" :key="i" :label="item.typeName" :value="item.type" />
            </el-select>
          </el-form-item>
          <el-form-item label="测点编号" prop="name">
            <el-input
              v-model="queryForm.name"
              placeholder="请输入测点编号"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
<!--            <el-button type="text" @click="handleFold">
              <span v-if="fold">展开</span>
              <span v-else>合并</span>
              <vab-icon
                class="vab-dropdown"
                :class="{ 'vab-dropdown-active': fold }"
                icon="arrow-up-s-line"
              />
            </el-button> -->
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd" v-permissions="{permission:['deviceParams:add']}">
          新增
        </el-button>
        <el-button icon="el-icon-edit" type="primary" @click="handleEditAlarm" v-permissions="{permission:['deviceParams:update']}">
          一键预警值配置
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
      </el-table-column>

      <el-table-column align="center" label="操作" width="280">
        <template #default="{ row }">
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row) "
            v-permissions="{permission:['deviceParams:update']}"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleClear(row)"
            v-permissions="{permission:['deviceParams:update']}"
          >
            清空
          </el-button>
          <el-button icon="el-icon-view" style="margin: 0 10px 0 0 !important" type="success"
            @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" @fetch-data="fetchData" />
    <alarmTableEdit ref="alarmEdit" @fetch-data="fetchData" />
    <Table-detail ref="detail" />

  </div>
</template>

<script>
  import { getDictList } from '@/api/system/dict-api'
  import {getDeviceParamsByPage, deleteDeviceParams, saveDeviceParams} from '@/api/device/deviceParams-api.js'
  import { getDeviceTypeByProjectId } from '@/api/online/online-api'
  import TableEdit from './components/alarmModelEdit'
  import TableDetail from './components/DeviceParamsEditDetail.vue'
  import alarmTableEdit from './components/DeviceAlarmDataEdit'
  import tableMix from '@/views/mixins/table'
  import {getProjectListWithAuth} from "@/api/project/project-api";

  export default {
    name: 'user',
    components: {
      TableDetail,
      TableEdit,
      alarmTableEdit
    },
    mixins: [tableMix],
    data() {
      return {
        selectDeviceType: [],
        queryData: {
          deviceType: ''
        },
        checkList: ['项目名称','测点编号','监测项', '一级报警下限','一级报警上限','二级报警下限','二级报警上限'],
        columns: [
          {
            label: '项目名称',
            prop: 'projectName',
            disableCheck: true,
          },
          {
            label: '测点编号',
            prop: 'name',
            disableCheck: true,
          },
          {
            label: '监测项',
            prop: 'deviceTypeName',
          },
          {
            label: '一级报警下限',
            prop: 'alarmDown1',
          },
          {
            label: '一级报警上限',
            prop: 'alarmUp1',
          },
          {
            label: '二级报警下限',
            prop: 'alarmDown2',
          },
          {
            label: '二级报警上限',
            prop: 'alarmUp2',
          },
        ],
        queryForm: {
          projectId:'',
          name:'',
          type: ''
        },
        projectList:[]
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    mounted() {
      this.getDictDetails()
      this.getProjectList()
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      // 获取桥对应的所有监测项
      async getDeviceType() {
        const deviceTypeByProjectIdRes = await getDeviceTypeByProjectId({
          projectId: this.queryForm.projectId,
        })
        this.selectDeviceType = deviceTypeByProjectIdRes.result
        // alert(this.selectDeviceType)

      },
      backDepart(list){
        return list.map(item=> item.departName).join(',')
      },
      backRolelist(list){
        return list.map(item=> item.roleName).join(',')
      },
      handleAdd() {
        this.$refs['edit'].showEdit()
      },
      handleEditAlarm(){
         this.$refs['alarmEdit'].showEdit()
      },
      handleEdit(row) {
          this.$refs['edit'].showEdit(row)
      },
      handleDetail(row) {
        this.$refs['detail'].showDialog(row)
      },
      handleClear(row) {
        if (row.id) {
          this.$baseConfirm('你确定要批量删除设备数据', null, async () => {
          row.alarmDown1 = ''
          row.alarmDown2 = ''
          row.alarmDown3 = ''
          row.alarmDown4 = ''
          row.alarmUp1 = ''
          row.alarmUp2 = ''
          row.alarmUp3 = ''
          row.alarmUp4 = ''
          row.rateDown1 = ''
            row.rateDown2 = ''
            row.rateDown3 = ''
            row.rateDown4 = ''
            row.rateUp1 = ''
            row.rateUp2 = ''
            row.rateUp3 = ''
            row.rateUp4 = ''
            row.isAdd = false
            saveDeviceParams(this.row).then(res => {
              this.$baseMessage(
                this.formData.isAdd ? '新增设备成功！' : '修改设备成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ? '新增设备失败！' : '修改设备失败!',
                'error', 'vab-hey-message-error'
              )
            })
          })
        }
      },
      // 从数据字典获取数据
      async getDictDetails() {
        this.listLoading = true
        let param = {dictCode: "deviceType"}
        const deviceTypeRes = await getDictList(param)
        const deviceTypeResult = deviceTypeRes.result[0].children
        for (const data of deviceTypeResult) {
          const treeNode = {}
          treeNode.id = data.dictCode
          treeNode.label = data.dictName
          treeNode.disabled = false
          // this.selectDeviceType.push({...data,...treeNode})
        }
        this.listLoading = false
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result: { records, total }} = await getDeviceParamsByPage(this.queryForm)
        // console.log(records);
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      }
    },
  }
</script>
<style lang="scss" scoped>

</style>
