<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    title="设备参数信息详情"
    :visible.sync="dialogDetailVisible"
    width="60%"
    top="20vh"
    center
    v-drag
  >
          <el-descriptions title="基本信息" class="margin-top" :column="3" border size="medium">
          <el-descriptions-item :labelStyle='labelStyle' :contentStyle='typeContentStyle'>
            <template slot="label">监测项</template>
            {{formData.deviceTypeName}}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle='labelStyle' :contentStyle='typeContentStyle'>
            <template slot="label">测点编号</template>
            {{formData.name}}
          </el-descriptions-item>
          <el-descriptions-item :labelStyle='labelStyle'>
            <template slot="label">基础公式</template>
            {{formData.baseLine}}
          </el-descriptions-item>
          <el-descriptions-item v-if="formData.type == 'survey'" :labelStyle='labelStyle'>
            <template slot="label">深度值</template>
            {{formData.deep}}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="累计预警值" class="margin-top title" :column="4" border size="medium">
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">一级报警下限</template>
            {{formData.alarmDown1}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">一级报警上限</template>
            {{formData.alarmUp1}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">二级报警下限</template>
            {{formData.alarmDown2}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">二级报警上限</template>
            {{formData.alarmUp2}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">三级报警下限</template>
            {{formData.alarmDown3}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">三级报警上限</template>
            {{formData.alarmUp3}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">四级报警下限</template>
            {{formData.alarmDown4}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">四级报警上限</template>
            {{formData.alarmUp4}}
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions title="月速率预警值" class="margin-top title" :column="4" border size="medium">
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">一级报警下限</template>
            {{formData.rateDown1}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">一级报警上限</template>
            {{formData.rateUp1}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">二级报警下限</template>
            {{formData.rateDown2}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">二级报警上限</template>
            {{formData.rateUp2}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">三级报警下限</template>
            {{formData.rateDown3}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">三级报警上限</template>
            {{formData.rateUp3}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">四级报警下限</template>
            {{formData.rateDown4}}
          </el-descriptions-item>
          <el-descriptions-item :contentStyle='contentStyle' :labelStyle='labelStyle'>
            <template slot="label">四级报警上限</template>
            {{formData.rateUp4}}
          </el-descriptions-item>
        </el-descriptions>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  export default {
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
        //内容样式
        contentStyle: {
          'text-align': 'center',
        },
        typeContentStyle: {
          'width': '260px'
        },
        //label样式
        labelStyle: { 'width': '200px' }
      }
    },
    methods: {
      showDialog(row) {
        this.formData = { ...row }
        this.dialogDetailVisible=true
      },
    }
  }
</script>

<style scoped>
.title {
  margin-top: 20px;
}
</style>
