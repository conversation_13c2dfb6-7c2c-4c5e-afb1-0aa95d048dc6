<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1650px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <table class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr>
          <td>
            <el-form-item label="项目选择" prop="projectId">
              <el-select
                ref="project"
                v-model="formData.projectId"
                filterable
                size="medium"
                style="width: 280px"
                @change="changeProject"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="监测项:" prop="type">
              <el-select
                v-model="formData.type"
                placeholder="选择监测项"
                style="width: 280px"
                @change="changeType()"
              >
                <el-option
                  v-for="(item, i) in selectDeviceType"
                  :key="i"
                  :label="item.typeName"
                  :value="item.type"
                />
              </el-select>
            </el-form-item>
          </td>
        </tr>
        <tr class="title">
          <td colspan="3">
            <alarm-params
              v-for="(item, index) in alarmNum"
              :key="item"
              :alarm-params="alarmParams[index]"
              :label="item"
              :title="item"
            />
          </td>
        </tr>
      </table>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getDeviceTypeByProjectId } from '@/api/online/online-api'
  import { updateAlarmByType } from '@/api/device/deviceParams-api.js'
  import alarmParams from './AlarmParams'
  import { getProjectListWithAuth } from '@/api/project/project-api'

  export default {
    name: 'AlarmTableEdit',
    components: {
      alarmParams,
    },
    props: {},
    data() {
      return {
        selectDeviceType: [],
        projectList: [], // 添加项目列表数据属性
        title: '',
        dialogFormVisible: false,
        formData: {},
        alarmNum: [''],
        alarmParams: [{}, {}, {}],
        rules: {
          type: [
            { required: true, message: '监测项为必选', trigger: 'change' },
          ],
        },
      }
    },
    created() {},
    mounted() {
      this.getProjectList() // 在组件挂载时调用获取项目列表
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '预警值配置'
          this.initForm()
          this.changeType()
          this.alarmParams = [{}, {}, {}]
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 获取桥对应的所有设备类型
      async getDeviceType(projectId = null) {
        // 如果传入了 projectId 参数则使用，否则使用表单中选中的 projectId
        const targetProjectId = projectId || this.formData.projectId
        if (!targetProjectId) {
          this.selectDeviceType = []
          return
        }

        try {
          const deviceTypeByProjectIdRes = await getDeviceTypeByProjectId({
            projectId: targetProjectId,
          })
          this.selectDeviceType = deviceTypeByProjectIdRes.result || []
        } catch (err) {
          console.error('获取设备类型失败:', err)
          this.selectDeviceType = []
        }
      },
      changeType() {
        if (this.formData.type === 'gnss') {
          this.alarmNum = ['X轴', 'Y轴', 'Z轴']
        } else if (this.formData.type === 'incline') {
          this.alarmNum = ['X轴', 'Y轴']
        } else if (this.formData.type === 'humid') {
          this.alarmNum = ['温度', '湿度']
        } else {
          this.alarmNum = ['']
        }
      },
      // 初始化表单
      initForm() {
        this.formData = {
          type: '',
          projectId: '',
          alarmDown1: '',
          alarmUp1: '',
          alarmDown2: '',
          alarmUp2: '',
          alarmDown3: '',
          alarmUp3: '',
          alarmDown4: '',
          alarmUp4: '',
          rateDown1: '',
          rateUp1: '',
          rateDown2: '',
          rateUp2: '',
          rateDown3: '',
          rateUp3: '',
          rateDown4: '',
          rateUp4: '',
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.concatFormData()
            // 使用用户在选择框中选中的 projectId，不再使用 sessionStorage
            updateAlarmByType(this.formData)
              .then((res) => {
                this.$baseMessage(
                  '配置预警值成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  '配置预警值失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      getProjectList() {
        getProjectListWithAuth({})
          .then((res) => {
            this.projectList = res.result || []
          })
          .catch((err) => {
            console.error('获取项目列表失败:', err)
            this.projectList = []
          })
      },
      changeProject() {
        const selectedProject = this.projectList.find(
          (p) => p.id === this.formData.projectId
        )
        if (selectedProject && selectedProject.position) {
          this.formData.position = selectedProject.position
        } else {
          this.formData.position = ''
        }

        // 当项目改变时，重新获取该项目对应的设备类型
        this.getDeviceType(this.formData.projectId)
        // 清空监测项选择
        this.formData.type = ''
      },
      /**
       * 更新时拼装formData
       */
      concatFormData() {
        this.formData.alarmDown1 = ''
        this.formData.alarmDown2 = ''
        this.formData.alarmDown3 = ''
        this.formData.alarmDown4 = ''
        this.formData.alarmUp1 = ''
        this.formData.alarmUp2 = ''
        this.formData.alarmUp3 = ''
        this.formData.alarmUp4 = ''
        this.formData.rateDown1 = ''
        this.formData.rateDown2 = ''
        this.formData.rateDown3 = ''
        this.formData.rateDown4 = ''
        this.formData.rateUp1 = ''
        this.formData.rateUp2 = ''
        this.formData.rateUp3 = ''
        this.formData.rateUp4 = ''
        this.alarmNum.forEach((item, i) => {
          this.formData.alarmDown1 +=
            (this.alarmParams[i].alarmDown1
              ? this.alarmParams[i].alarmDown1
              : '') + ','
          this.formData.alarmDown2 +=
            (this.alarmParams[i].alarmDown2
              ? this.alarmParams[i].alarmDown2
              : '') + ','
          this.formData.alarmDown3 +=
            (this.alarmParams[i].alarmDown3
              ? this.alarmParams[i].alarmDown3
              : '') + ','
          this.formData.alarmDown4 +=
            (this.alarmParams[i].alarmDown4
              ? this.alarmParams[i].alarmDown4
              : '') + ','
          this.formData.alarmUp1 +=
            (this.alarmParams[i].alarmUp1 ? this.alarmParams[i].alarmUp1 : '') +
            ','
          this.formData.alarmUp2 +=
            (this.alarmParams[i].alarmUp2 ? this.alarmParams[i].alarmUp2 : '') +
            ','
          this.formData.alarmUp3 +=
            (this.alarmParams[i].alarmUp3 ? this.alarmParams[i].alarmUp3 : '') +
            ','
          this.formData.alarmUp4 +=
            (this.alarmParams[i].alarmUp4 ? this.alarmParams[i].alarmUp4 : '') +
            ','
          this.formData.rateDown1 +=
            (this.alarmParams[i].rateDown1
              ? this.alarmParams[i].rateDown1
              : '') + ','
          this.formData.rateDown2 +=
            (this.alarmParams[i].rateDown2
              ? this.alarmParams[i].rateDown2
              : '') + ','
          this.formData.rateDown3 +=
            (this.alarmParams[i].rateDown3
              ? this.alarmParams[i].rateDown3
              : '') + ','
          this.formData.rateDown4 +=
            (this.alarmParams[i].rateDown4
              ? this.alarmParams[i].rateDown4
              : '') + ','
          this.formData.rateUp1 +=
            (this.alarmParams[i].rateUp1 ? this.alarmParams[i].rateUp1 : '') +
            ','
          this.formData.rateUp2 +=
            (this.alarmParams[i].rateUp2 ? this.alarmParams[i].rateUp2 : '') +
            ','
          this.formData.rateUp3 +=
            (this.alarmParams[i].rateUp3 ? this.alarmParams[i].rateUp3 : '') +
            ','
          this.formData.rateUp4 +=
            (this.alarmParams[i].rateUp4 ? this.alarmParams[i].rateUp4 : '') +
            ','
        })
        this.formData.alarmDown1 = this.formData.alarmDown1.substring(
          0,
          this.formData.alarmDown1.length - 1
        )
        this.formData.alarmDown2 = this.formData.alarmDown2.substring(
          0,
          this.formData.alarmDown2.length - 1
        )
        this.formData.alarmDown3 = this.formData.alarmDown3.substring(
          0,
          this.formData.alarmDown3.length - 1
        )
        this.formData.alarmDown4 = this.formData.alarmDown4.substring(
          0,
          this.formData.alarmDown4.length - 1
        )
        this.formData.alarmUp1 = this.formData.alarmUp1.substring(
          0,
          this.formData.alarmUp1.length - 1
        )
        this.formData.alarmUp2 = this.formData.alarmUp2.substring(
          0,
          this.formData.alarmUp2.length - 1
        )
        this.formData.alarmUp3 = this.formData.alarmUp3.substring(
          0,
          this.formData.alarmUp3.length - 1
        )
        this.formData.alarmUp4 = this.formData.alarmUp4.substring(
          0,
          this.formData.alarmUp4.length - 1
        )
        this.formData.rateDown1 = this.formData.rateDown1.substring(
          0,
          this.formData.rateDown1.length - 1
        )
        this.formData.rateDown2 = this.formData.rateDown2.substring(
          0,
          this.formData.rateDown2.length - 1
        )
        this.formData.rateDown3 = this.formData.rateDown3.substring(
          0,
          this.formData.rateDown3.length - 1
        )
        this.formData.rateDown4 = this.formData.rateDown4.substring(
          0,
          this.formData.rateDown4.length - 1
        )
        this.formData.rateUp1 = this.formData.rateUp1.substring(
          0,
          this.formData.rateUp1.length - 1
        )
        this.formData.rateUp2 = this.formData.rateUp2.substring(
          0,
          this.formData.rateUp2.length - 1
        )
        this.formData.rateUp3 = this.formData.rateUp3.substring(
          0,
          this.formData.rateUp3.length - 1
        )
        this.formData.rateUp4 = this.formData.rateUp4.substring(
          0,
          this.formData.rateUp4.length - 1
        )
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
