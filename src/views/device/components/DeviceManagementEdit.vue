<template>
  <el-dialog
    v-drag
    append-to-body
    center
    :close-on-click-modal="false"
    :title="title"
    top="5vh"
    :visible.sync="dialogFormVisible"
    width="1650px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title"><td colspan="3">基本信息</td></tr>
        <tr>
          <td>
            <el-form-item label="项目选择" prop="projectId">
              <el-select
                ref="project"
                v-model="formData.projectId"
                filterable
                size="medium"
                style="width: 280px"
                @change="changeProject"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="设备编号" prop="code">
              <el-input v-model="formData.code" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="formData.name" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="设备类型:" prop="type">
              <el-select
                v-model="formData.type"
                :disabled="!formData.isAdd"
                placeholder="选择设备类型"
                style="width: 280px"
                @change="deviceTypeChange"
              >
                <el-option
                  v-for="item in selectDeviceType"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="品牌">
              <el-select
                v-model="formData.brand"
                placeholder="选择设备品牌"
                style="width: 280px"
                @change="deviceTypeChange"
              >
                <el-option
                  v-for="item in selectDeviceBrand"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="设备规格">
              <el-input v-model="formData.specification" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr v-if="formData.type === 'floor'">
          <td :colspan="formData.isBase === 'n' ? 1 : 3">
            <el-form-item label="是否基点" prop="isBase">
              <el-radio-group
                v-model="formData.isBase"
                :disabled="!formData.isAdd"
                @input="baseChange"
              >
                <el-radio label="y">是</el-radio>
                <el-radio label="n">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </td>
          <td v-if="formData.isBase === 'n'">
            <el-form-item label="关联基点设备" prop="associateDevice">
              <el-select
                v-model="formData.associateDevice"
                style="width: 280px"
              >
                <el-option
                  v-for="item in baseDeviceType"
                  :key="item.id"
                  :label="item.code"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
        </tr>
        <tr v-if="formData.type === 'survey'">
          <td colspan="3">
            <el-form-item label="深度" prop="deep">
              <el-input v-model="formData.deep" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr v-if="formData.type === 'gnss'">
          <td colspan="3">
            <el-form-item label="坡体方向角" prop="deep">
              <el-input v-model="formData.deep" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="安装人员">
              <el-input v-model="formData.installName" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="安装状态">
              <el-input
                v-model="formData.installationStatus"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="安装位置">
              <el-input v-model="formData.location" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="安装时间">
              <el-date-picker
                v-model="formData.installTime"
                style="width: 280px"
                type="date"
              />
            </el-form-item>
          </td>

          <td>
            <el-form-item label="孔位/断面数">
              <el-input v-model="formData.sectionNum" style="width: 280px" />
            </el-form-item>
          </td>

          <td>
            <el-form-item label="序号">
              <el-input v-model="formData.sort" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="sn号">
              <el-input v-model="formData.snNum" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="采集频率">
              <el-input
                v-model="formData.samplingFrequency"
                style="width: 280px"
              />
            </el-form-item>
          </td>
          <td>
            <!-- <el-form-item label="经纬度" @click.native="showBMap">
              <el-input v-model="formData.position" style="width: 280px" />
            </el-form-item> -->
            <el-form-item label="经纬度">
              <el-input v-model="formData.position" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="DTU编号">
              <el-input v-model="formData.dtuNo" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="端口">
              <el-input v-model="formData.port" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="ip地址">
              <el-input v-model="formData.ip" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td>
            <el-form-item label="通道号">
              <el-input v-model="formData.gatewayNo" style="width: 280px" />
            </el-form-item>
          </td>
          <td>
            <el-form-item label="模块号">
              <el-input v-model="formData.moduleNo" style="width: 280px" />
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                rows="3"
                style="width: 815px"
                type="textarea"
              />
            </el-form-item>
          </td>
        </tr>
      </table>
    </el-form>
    <!-- <BMapPopup ref="BMapPopup" @callBackSite="callBackSite" /> -->
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils.js'
  import { mapGetters } from 'vuex'
  import { getDictList } from '@/api/system/dict-api'
  import {
    saveDeviceManagement,
    checkDevice,
    getDeviceByType,
  } from '@/api/device/deviceManagement-api.js'
  import {
    getDataProject,
    getProjectListWithAuth,
  } from '@/api/project/project-api'
  // import BMapPopup from '@/views/common/BMap/BMapPopup.vue'
  export default {
    name: 'TableEdit',
    // components: { BMapPopup },
    props: {},
    data() {
      const validateDeviceCode = (rule, value, callback) => {
        if (!this.formData.isAdd) {
          callback()
          return
        }
        // 校验设备是否存在!
        checkDevice({ code: value, projectId: this.formData.projectId })
          .then((response) => {
            callback()
          })
          .catch((err) => {
            callback(new Error('设备编号已存在!'))
          })
      }
      return {
        projectList: [],
        selectDeviceType: [],
        selectDeviceBrand: [],
        baseDeviceType: [],
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          projectId: [
            { required: true, message: '项目名称为必选', trigger: 'blur' },
          ],
          code: [
            { required: true, message: '设备编号为必填', trigger: 'blur' },
            { required: true, validator: validateDeviceCode, trigger: 'blur' },
          ],
          name: [
            { required: true, message: '设备名称为必填', trigger: 'change' },
          ],
          type: [
            { required: true, message: '设备类型为必选', trigger: 'blur' },
          ],
          isBase: [
            { required: true, message: '是否是基点设备', trigger: 'change' },
          ],
          associateDevice: [
            { required: true, message: '选择基点关联设备', trigger: 'change' },
          ],
          deep: [{ required: true, message: '输入深度值', trigger: 'blur' }],
        },
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
      }),
    },
    created() {},
    mounted() {
      this.getProjectList()
      this.getDictDetails()
    },
    methods: {
      // callBackSite(data) {
      //   this.formData.position = data[0]
      //   this.$forceUpdate()
      // },
      // showBMap() {
      //   this.$refs.BMapPopup.showBMap(this.formData)
      // },
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      changeProject() {
        const selectedProject = this.projectList.find(
          (p) => p.id === this.formData.projectId
        )
        if (selectedProject && selectedProject.position) {
          this.formData.position = selectedProject.position
        } else {
          this.formData.position = ''
        }
      },
      async showEdit(row) {
        if (!row) {
          this.title = '新增设备'
          this.initForm()
        } else {
          this.title = '编辑设备'
          this.formData = Object.assign({ isAdd: false }, row)
          if (this.formData.associateDevice) {
            await this.getDeviceByType()
          }
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      baseChange() {
        if (this.formData.isBase === 'n' && !this.baseDeviceType.length) {
          this.getDeviceByType()
        }
      },
      async getDeviceByType() {
        const paramsData = {
          projectId: this.formData.projectId,
          type: this.formData.type,
          isBase: 'y',
        }
        await getDeviceByType(paramsData).then((res) => {
          this.baseDeviceType = res.result
        })
      },
      deviceTypeChange() {
        this.formData.isBase = 'n'
        if (this.formData.type === 'floor') {
          this.formData.isBase = ''
          this.formData.associateDevice = ''
        } else if (this.formData.type === 'survey') {
          this.formData.deep = ''
        }
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          projectId: '',
          projectName: '',
          code: '',
          name: '',
          dtuNo: '',
          gatewayNo: '',
          moduleNo: '',
          port: '',
          unit: '',
          specification: '',
          type: '',
          brand: '',
          installName: '',
          position: '',
          installTime: '',
          seqNo: '',
          sectionNum: '',
          remark: '',
          isBase: '',
          associateDevice: '',
          deep: '',
          sort: '1',
          ip: '',
          installationStatus: '',
          snNum: '',
          samplingFrequency: '',
          location: '',
          isAdd: true,
        }
      },
      // 从数据字典获取数据
      async getDictDetails() {
        this.listLoading = true
        let param = { dictCode: 'deviceType' }
        const deviceTypeRes = await getDictList(param)
        const deviceTypeResult = deviceTypeRes.result[0].children
        for (const data of deviceTypeResult) {
          const treeNode = {}
          treeNode.id = data.dictCode
          treeNode.label = data.dictName
          treeNode.disabled = false
          this.selectDeviceType.push({ ...data, ...treeNode })
        }
        let param2 = { dictCode: 'brand_device' }
        const deviceBrandRes = await getDictList(param2)
        const deviceBrandResult = deviceBrandRes.result[0].children
        for (const data of deviceBrandResult) {
          const treeNode = {}
          treeNode.id = data.dictCode
          treeNode.label = data.dictName
          treeNode.disabled = false
          this.selectDeviceBrand.push({ ...data, ...treeNode })
        }
        this.listLoading = false
      },
      save() {
        this.formData.projectName = this.$refs.project.selected.label
        console.log(this.formData)
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveDeviceManagement(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增设备成功！' : '修改设备成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增设备失败！' : '修改设备失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = {}
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
