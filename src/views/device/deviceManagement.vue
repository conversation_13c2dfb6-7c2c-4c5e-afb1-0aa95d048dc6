<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
            <el-select v-model="queryForm.projectId" filterable size="medium">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="postInfo-container-item"
            label="监测项"
            label-width="80px"
            prop="type"
          >
            <el-select
              v-model="queryForm.type"
              placeholder="请选择监测项"
              style="width: 200px"
            >
              <el-option
                v-for="(item, i) in selectDeviceType"
                :key="i"
                :label="item.typeName"
                :value="item.type"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            class="postInfo-container-item"
            label="监测状态"
            label-width="80px"
            prop="monitoringStatus"
          >
            <el-select
              v-model="queryForm.monitoringStatus"
              placeholder="请选择监测状态"
              style="width: 200px"
            >
              <el-option label="在线" value="在线" />
              <el-option label="离线" value="离线" />
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="测点编号" prop="name">
            <el-input
              v-model="queryForm.name"
              placeholder="请输入测点编号"
            />
          </el-form-item> -->
          <el-form-item label="DTU编号" prop="dtuNo">
            <el-input v-model="queryForm.dtuNo" placeholder="请输入DTU编号" />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
            <el-button type="warning" @click="exportDataBtn">
              监测数据导出
            </el-button>
            <!--            <el-button type="text" @click="handleFold">
              <span v-if="fold">展开</span>
              <span v-else>合并</span>
              <vab-icon
                class="vab-dropdown"
                :class="{ 'vab-dropdown-active': fold }"
                icon="arrow-up-s-line"
              />
            </el-button> -->
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button
          v-permissions="{ permission: ['deviceManagement:add'] }"
          icon="el-icon-plus"
          type="primary"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          v-permissions="{ permission: ['deviceManagement:del'] }"
          icon="el-icon-delete"
          type="danger"
          @click="deleteDeviceManagement"
        >
          批删
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      />

      <el-table-column align="center" label="操作" width="310">
        <template #default="{ row }">
          <el-button
            v-permissions="{ permission: ['deviceManagement:update'] }"
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            v-permissions="{ permission: ['deviceManagement:del'] }"
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
          <el-button
            icon="el-icon-view"
            style="margin: 0 10px 0 0 !important"
            type="success"
            @click="handleDetail(row)"
          >
            详情
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" @fetch-data="fetchData" />
    <Table-detail ref="detail" />
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { getDictList } from '@/api/system/dict-api'
  // import { getDeviceTypeByProjectId } from '@/api/online/online-api'
  import {
    getDeviceManagementByPage,
    deleteDeviceManagement,
    getDeviceType,
    exportData,
  } from '@/api/device/deviceManagement-api.js'
  import TableEdit from './components/DeviceManagementEdit'
  import TableDetail from './components/DeviceManagementEditDetail.vue'
  import tableMix from '@/views/mixins/table'
  import { param } from '@/utils/th_utils'
  import { getProjectListWithAuth } from '@/api/project/project-api'

  export default {
    name: 'User',
    components: {
      TableEdit,
      TableDetail,
    },
    mixins: [tableMix],
    data() {
      return {
        selectDeviceType: [],
        checkList: [
          '项目名称',
          '监测项',
          '测点编号',
          '监测状态',
          '安装状态',
          '超时间隔（小时）',
          '最后活跃',
          'sn号',
          '采集频率',
          '经纬度',
        ],
        columns: [
          {
            label: '项目名称',
            prop: 'projectName',
            disableCheck: true,
          },
          {
            label: '监测项',
            prop: 'deviceTypeName',
            disableCheck: true,
          },
          // {
          //   label: '设备编号',
          //   prop: 'code',
          //   disableCheck: true,
          // },
          {
            label: '测点编号',
            prop: 'name',
            disableCheck: true,
          },
          // {
          //   label: 'DTU编号',
          //   prop: 'dtuNo',
          // },
          // {
          //   label: '通道号',
          //   prop: 'gatewayNo',
          // },
          // {
          //   label: '模块号',
          //   prop: 'moduleNo',
          // },
          // {
          //   label: '设备规格',
          //   prop: 'specification',
          // },
          // {
          //   label: '设备品牌',
          //   prop: 'brandName',
          // },
          // {
          //   label: '添加时间',
          //   prop: 'createTime',
          // },
          {
            label: '监测状态',
            prop: 'monitoringStatus',
          },
          {
            label: '安装状态',
            prop: 'installationStatus',
          },
          {
            label: '超时间隔（小时）',
            prop: 'timeoutInterval',
          },
          {
            label: '最后活跃',
            prop: 'lastActivity',
          },
          {
            label: 'sn号',
            prop: 'snNum',
          },
          {
            label: '采集频率',
            prop: 'samplingFrequency',
          },
          {
            label: '经纬度',
            prop: 'position',
          },
        ],
        queryForm: {
          projectId: '',
          dtuNo: '',
          monitoringStatus: '',
          type: '',
        },
        projectList: [],
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    mounted() {
      this.getProjectList()
      this.getDeviceType()
      this.getDictDetails()
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      getDeviceType() {
        getDeviceType({}).then((res) => {
          this.selectDeviceType = res.result || []
        })
      },
      exportDataBtn() {
        // this.queryForm.projectId = this.projectId
        // this.queryForm.type = this.type
        console.log(this.queryForm, '123562')
        exportData(this.queryForm).then((response) => {
          this.downloadFile(response, '设备清单', 'xlsx')
        })
      },
      // async getDeviceType() {
      //   const deviceTypeByProjectIdRes = await getDeviceTypeByProjectId({
      //     projectId: this.queryForm.projectId,
      //   })
      //   this.selectDeviceType = deviceTypeByProjectIdRes.result
      //   // alert(this.selectDeviceType)

      // },
      handleAdd() {
        this.$refs['edit'].showEdit()
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },
      handleDetail(row) {
        this.$refs['detail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前设备吗', null, async () => {
            deleteDeviceManagement({ id: row.id })
              .then(() => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '删除成功!',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch(() => {
                this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
              })
          })
        }
      },
      // 从数据字典获取数据
      async getDictDetails() {
        this.listLoading = true
        let param = { dictCode: 'deviceType' }
        const deviceTypeRes = await getDictList(param)
        const deviceTypeResult = deviceTypeRes.result[0].children
        for (const data of deviceTypeResult) {
          const treeNode = {}
          treeNode.id = data.dictCode
          treeNode.label = data.dictName
          treeNode.disabled = false
          // this.selectDeviceType.push({...data,...treeNode})
        }
        this.listLoading = false
      },
      deleteDeviceManagement() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量删除设备数据', null, async () => {
            await deleteDeviceManagement({ id: ids })
              .then((response) => {
                this.pageInfo.curPage = 1
                this.fetchData()
                this.$baseMessage(
                  '批量删除成功！',
                  'success',
                  'vab-hey-message-success'
                )
              })
              .catch((err) => {
                this.$baseMessage(
                  '批量删除失败！',
                  'error',
                  'vab-hey-message-error'
                )
              })
          })
        }
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const {
          result: { records, total },
        } = await getDeviceManagementByPage(this.queryForm)
        // console.log(records);
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>
<style lang="scss" scoped></style>
