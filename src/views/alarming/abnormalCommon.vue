<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
          <el-select v-model="queryForm.projectId" filterable size="medium" @change="getDeviceType">
            <el-option
              v-for="item in projectList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
          <el-form-item label-width="80px" label="监测项" class="postInfo-container-item" prop="type">
            <el-select v-model="queryForm.type" placeholder="请选择监测项" style="width:200px;" clearable>
              <el-option v-for="item in deviceTypes" :key="item.type" :label="item.typeName" :value="item.type"
              />
            </el-select>
          </el-form-item>

          <el-form-item label-width="80px" label="测点编号" class="postInfo-container-item" prop="deviceName">
            <el-input
              v-model="queryForm.deviceName" placeholder="请输入测点编号"/>
          </el-form-item>
          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
            <!--            <el-button type="text" @click="handleFold">
                          <span v-if="fold">展开</span>
                          <span v-else>合并</span>
                          <vab-icon
                            class="vab-dropdown"
                            :class="{ 'vab-dropdown-active': fold }"
                            icon="arrow-up-s-line"
                          />
                        </el-button> -->
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd"
                   v-permissions="{permission:['deviceManagement:add']}">
          新增
        </el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteAlertConfig"
                   v-permissions="{permission:['deviceManagement:del']}">
          批删
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height"/>
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line"/>
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :max-height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55"/>
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
          <span v-if="item.label == '监测项'">
            {{ formatType(row.type) }}
          </span>
          <span v-else>{{ row[item.prop] }}</span>
        </template>
      </el-table-column>

      <el-table-column align="center" label="操作" width="240">
        <template #default="{ row }">
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row) "
            v-permissions="{permission:['deviceManagement:update']}"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
            v-permissions="{permission:['deviceManagement:del']}"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" @fetch-data="fetchData"/>
  </div>
</template>

<script>
import {getDictList} from '@/api/system/dict-api'
import {
    getAbonrmalDatByPage, deleteAbonrmalDat
} from '@/api/alarming/abnormal-api.js'
import TableEdit from './components/AbnormalEdit'
import tableMix from '@/views/mixins/table'
import {getDeviceTypeByProjectId} from '@/api/online/online-api'
import {getDataDeviceManagement} from '@/api/device/deviceManagement-api'
import {getProjectListWithAuth} from "@/api/project/project-api";

export default {
    name: 'Abnormal',
    components: {
        TableEdit
    },
    mixins: [tableMix],
    data() {
        return {
            deviceTypes: [],
          selectDeviceType:[],
            deviceIdList: [],
            queryData: {
              deviceType: '',
              deviceName: ''
            },
            checkList: ['项目名称', '监测项','测点编号', '正常数据上限', '正常数据下限', '设备名称', '区值范围'],
            columns: [
                {
                    label: '项目名称',
                    prop: 'projectName',
                    disableCheck: true,
                },
                {
                  label: '监测项',
                  prop: 'type',
                  disableCheck: true,
                },
                {
                    label: '测点编号',
                    prop: 'deviceName',
                },
                {
                    label: '正常数据上限',
                    prop: 'maxVal',
                },
                {
                    label: '正常数据下限',
                    prop: 'minVal',
                },
                 {
                    label: '区值范围',
                    prop: 'zoneValue',
                },
                {
                    label: '添加人',
                    prop: 'createName',
                },
                {
                    label: '添加时间',
                    prop: 'createTime',
                },
                {
                    label: '修改人',
                    prop: 'updateName',
                },
                {
                    label: '修改时间',
                    prop: 'updateTime',
                },
            ],
            queryForm: {
              deviceId: [],
              type: '',
              projectId: '',
              deviceName: ''
            },
          projectList:[]
        }
    },
    computed: {
        finallyColumns() {
            return this.columns.filter((item) =>
                this.checkList.includes(item.label)
            )
        },
    },
    created() {
        this.projectId = sessionStorage.getItem('def_project_Id')
        this.getProjectList()
      this.getDictDetails()
        this.fetchData()
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      // 获取设备类型名称
      formatType(type) {
        for(const item of this.selectDeviceType) {
          if (item.id == type) {
            return item.dictName
          }
        }
      },
        /**
         * 获取桥对应的所有设备类型
         */
        getDeviceType() {
            getDeviceTypeByProjectId({projectId: this.queryForm.projectId})
                .then((res) => {
                    this.deviceTypes = res.result
                })
                .catch(() => {
                })
        },
      // 从数据字典获取数据
      async getDictDetails() {
        this.listLoading = true
        let param = {dictCode: "deviceType"}
        const deviceTypeRes = await getDictList(param)
        const deviceTypeResult = deviceTypeRes.result[0].children
        for (const data of deviceTypeResult) {
          const treeNode = {}
          treeNode.id = data.dictCode
          treeNode.label = data.dictName
          treeNode.disabled = false
          this.selectDeviceType.push({...data,...treeNode})
        }
        console.info(this.selectDeviceType)
      },
        handleAdd() {
            this.$refs['edit'].showEdit()
        },
        handleEdit(row) {
          row.typeName = this.formatType(row.type)
          this.$refs['edit'].showEdit(row)
        },
        handleDelete(row) {
            if (row.id) {
                this.$baseConfirm('你确定要删除该异常信息配置！', null, async () => {
                    deleteAbonrmalDat({id: row.id}).then(() => {
                        this.pageInfo.curPage = 1
                        this.fetchData()
                        this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success')
                    })
                        .catch(() => {
                            this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
                        })
                })
            }
        },
        deleteAlertConfig() {
            if (this.selectRows.length == 0) {
                this.$baseMessage(
                    '请选中最少一条记录!',
                    'error',
                    'vab-hey-message-error'
                )
            } else {
                const ids = this.selectRows.map((item) => item.id).join(',')
                this.$baseConfirm('你确定要批量删除配置用户', null, async () => {
                    await deleteAbonrmalDat({id: ids}).then(response => {
                        this.pageInfo.curPage = 1
                        this.fetchData()
                        this.$baseMessage('批量删除成功！', 'success', 'vab-hey-message-success')
                    }).catch(err => {
                        this.$baseMessage('批量删除失败！', 'error', 'vab-hey-message-error')
                    })
                })
            }
        },
        async fetchData() {
            this.queryForm.curPage = this.pageInfo.curPage
            this.queryForm.pageSize = this.pageInfo.pageSize
            this.listLoading = true
            const {result: {records, total}} = await getAbonrmalDatByPage(this.queryForm)
            // console.log(records);
            this.list = records
            this.pageInfo.total = Number(total)
            this.listLoading = false
        },
    },
}
</script>
<style lang="scss" scoped>

</style>
