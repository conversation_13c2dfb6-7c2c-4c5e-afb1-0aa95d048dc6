<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
            <el-select v-model="queryForm.projectId" filterable size="medium">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="用户名" prop="userName">
            <el-input
              v-model="queryForm.userName"
              placeholder="请输入用户名"
            />
          </el-form-item>
          <el-form-item label="电话" prop="phone">
            <el-input
              v-model="queryForm.phone"
              placeholder="请输入电话号码"
            />
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-plus" type="primary" @click="handleAdd" v-permissions="{permission:['alertConfig:add']}">
          新增
        </el-button>
        <el-button icon="el-icon-delete" type="danger" @click="deleteAlertConfig" v-permissions="{permission:['alertConfig:del']}">
          批删
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
      </el-table-column>

      <el-table-column align="center" label="操作" width="280">
        <template #default="{ row }">
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row) "
            v-permissions="{permission:['alertConfig:update']}"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
            v-permissions="{permission:['alertConfig:del']}"
          >
            删除
          </el-button>
          <el-button icon="el-icon-view" style="margin: 0 10px 0 0 !important" type="success"
            @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" @fetch-data="fetchData" />
    <alarmConfigDetail ref="alarmConfigDetail" />
  </div>
</template>

<script>
  import {  getAlertConfigByPage,deleteAlertConfig} from '@/api/alarming/alertConfig-api'
  import TableEdit from './components/AlertConfigEdit'
  import alarmConfigDetail from './components/alarmConfigDetail.vue'
  import tableMix from '@/views/mixins/table'
  import { param } from '@/utils/th_utils'
  import {checkPermissions } from '@/api/system/permissions-api'
  import {getProjectListWithAuth} from "@/api/project/project-api";
  export default {
    name: 'alertConfig',
    components: {
      TableEdit,alarmConfigDetail
    },
    mixins: [tableMix],
    data() {
      return {
        projectList:[],
        queryData: {
          projectId: '',
          deviceType: ''
        },
        checkList: ['项目名称','用户名', '电话','所在公司','预警等级','邮箱', '添加人','添加时间','修改人','修改时间'],
        columns: [
          {
            label: '项目名称',
            prop: 'projectName',
            disableCheck: true,
          },
          {
            label: '用户名',
            prop: 'userName',
            disableCheck: true,
          },
          {
            label: '电话',
            prop: 'phone',
          },
          {
            label: '所在公司',
            prop: 'company',
          },
          {
            label: '预警等级',
            prop: 'level',
          },
          {
            label: '邮箱',
            prop: 'email',
          },
          // {
          //   label: '添加人',
          //   prop: 'createName',
          // },
          // {
          //   label: '添加时间',
          //   prop: 'createTime',
          // },
          // {
          //   label: '修改人',
          //   prop: 'updateName',
          // },
          // {
          //   label: '修改时间',
          //   prop: 'updateTime',
          // },
        ],
        projectId:'',
        queryForm: {
          projectId:'',
          userName:'',
          phone:'',
          type: '0'
        },
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.fetchData()
    },
    mounted() {
      this.getProjectList()
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      handleAdd() {
        this.$refs['edit'].showEdit('')
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row,'')
      },
      handleDetail(row) {
        this.$refs['alarmConfigDetail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除配置用户！', null, async () => {
            deleteAlertConfig({id:row.id}).then(() => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('删除成功!','success','vab-hey-message-success')
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      deleteAlertConfig() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量删除配置用户', null, async () => {
            await deleteAlertConfig({id:ids}).then(response=>{
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('批量删除成功！', 'success', 'vab-hey-message-success')
            }).catch(err=>{
              this.$baseMessage('批量删除失败！', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.queryForm.projectId = this.projectId
        this.listLoading = true
        const { result: { records, total }} = await getAlertConfigByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>
<style lang="scss" scoped>

</style>
