<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item  label="设备类型:" prop="deviceType">
            <el-select v-model="queryForm.deviceType" placeholder="选择设备类型" style="width:200px;">
              <el-option v-for="item in selectDeviceType" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-right-panel :span="24">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :max-height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
  import { getDictList } from '@/api/system/dict-api'
  import { getInterruptEmailByPage,deleteInterruptEmail} from '@/api/alarming/interruptEmail-api.js'
  import tableMix from '@/views/mixins/table'
  import { param } from '@/utils/th_utils'

  export default {
    name: 'interruptEmail',
    mixins: [tableMix],
    data() {
      return {
        selectDeviceType: [],
        queryData: {
          deviceType: ''
        },
        checkList: ['项目名称','设备名称','设备类型','最新数据采集时间'],
        columns: [
          {
            label: '项目名称',
            prop: 'projectName',
            disableCheck: true,
          },
          {
            label: '设备名称',
            prop: 'deviceName',
          },
          {
            label: '设备类型',
            prop: 'deviceTypeName',
          },
          {
            label: '最新数据采集时间',
            prop: 'prodTime',
          },
        ],
        queryForm: {
          projectId:'',
          deviceType:''
        },
        projectId:'',
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.projectId = sessionStorage.getItem('def_project_Id')
      this.fetchData()
    },
    mounted() {
      this.getDictDetails()
    },
    methods: {
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除数据吗', null, async () => {
            deleteDeviceManagement({id:row.id}).then(() => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('删除成功!','success','vab-hey-message-success')
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      getDictDetails() {
        getDictList({ dictCode: 'deviceType' }).then((res) => {
          const result = res.result[0].children
          this.selectDeviceType = result.map(item => {
            return {
              label: item.dictName,
              value: item.dictCode
            }
          })
        })
      },
      deleteDeviceManagement() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请选中最少一条记录!',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量删除数据', null, async () => {
            await deleteDeviceManagement({id:ids}).then(response=>{
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('批量删除成功！', 'success', 'vab-hey-message-success')
            }).catch(err=>{
              this.$baseMessage('批量删除失败！', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.queryForm.projectId = this.projectId
        this.listLoading = true
        const { result: { records, total }} = await getInterruptEmailByPage(this.queryForm)
        // console.log(records);
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>
<style lang="scss" scoped>

</style>
