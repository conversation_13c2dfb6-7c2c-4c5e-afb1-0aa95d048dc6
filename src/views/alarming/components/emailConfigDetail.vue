<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    title="中断邮件预警配置详情"
    :visible.sync="dialogDetailVisible"
    width="1250px"
    top="5vh"
    center
    v-drag
  >

    <!-- <div class="form-table-title">基本信息</div> -->
    <el-descriptions class="margin-top" :column="3" border size="medium">
      <el-descriptions-item>
        <template slot="label">项目名称</template>
        {{formData.projectName}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">用户名</template>
        {{formData.userName}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">电话</template>
        {{formData.phone }}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">邮箱</template>
        {{formData.email}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">添加人</template>
        {{formData.createName}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">添加时间</template>
        {{formData.createTime}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">修改人</template>
        {{formData.updateName}}
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label">修改时间</template>
        {{formData.updateTime}}
      </el-descriptions-item>
    </el-descriptions>

    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import { parseTime } from '@/utils'
export default {
  props: {
    default() {
      return []
    }

  },
  components: {

  },
  data() {
    return {
      dialogDetailVisible: false,
      formData: {},
    }
  },
  methods: {
    dateFormat(date) {
      if (!date) {
        return ''
      }
      return parseTime(new Date(date), '{y}-{m}-{d}')
    },
    async showDialog(row) {
      this.formData = { ...row }
      this.dialogDetailVisible=true
    },
  }
}
</script>

<style lang="scss" scoped>
</style>
