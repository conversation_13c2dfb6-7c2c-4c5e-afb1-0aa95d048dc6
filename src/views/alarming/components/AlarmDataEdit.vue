<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
      <el-form-item label="处理状态:" prop="status">
        <template>
          <el-radio v-model="formData.status"  label="-1">发送(业主)</el-radio>
          <el-radio v-model="formData.status" label="1">忽略</el-radio>
        </template>
      </el-form-item>
      <el-form-item label="处理状态:" prop="status">
        <el-input v-model="formData.remark"
                  type="textarea"
                  autocomplete="off" style="width: 280px;" />
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { mapGetters } from 'vuex'
  import { deal } from '@/api/alarming/alarmData-api.js'
  export default {
    props:{},
    data() {
      return {
        selectAlertStatus: [],
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
      }),
    },
    created() {

    },
    mounted() {

    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增预警'
          this.initForm()
        } else {
          this.title = '预警处理'
          this.formData = Object.assign({ isAdd: false,}, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          type: '',
          isAdd: true
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            deal(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ? '预警信息处理成功！' : '预警信息处理成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ? '预警信息处理失败！' : '预警信息处理失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
