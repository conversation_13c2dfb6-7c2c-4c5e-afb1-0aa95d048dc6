<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1350px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="120px"
    >
      <table v-loading="formloading" class="form-table">
        <tr class="title">
          <td colspan="3">基本信息</td>
        </tr>
        <tr v-if="formData.isAdd">
          <td>
            <el-form-item label="项目选择" prop="projectId">
              <el-select v-model="formData.projectId" filterable size="medium"  style="width: 200px;" @change="getDeviceType">
                <el-option
                  v-for="item in projectList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item prop="type" label="监测项" class="postInfo-container-item">
              <el-select v-model="formData.type"  placeholder="选择设备类型" style="width:200px;"
                         clearable
                         @change="changeType()">
                <el-option v-for="item in deviceTypes" :key="item.type" :label="item.typeName" :value="item.type"/>
              </el-select>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="测点编号" prop="deviceId" class="postInfo-container-item">
              <el-select v-model="formData.deviceId"  placeholder="选择设备" style="width:200px;"
                         clearable collapse-tags>
                <el-option v-for="item in deviceList" :key="item.id" :label="item.name" :value="item.id"/>
              </el-select>
            </el-form-item>
          </td>
        </tr>
        <tr v-else>
          <td>
            <el-form-item label="项目选择" prop="projectId">
              <el-input v-model="formData.projectName" disabled style="width:200px;"/>
            </el-form-item>
          </td>
          <td>
            <el-form-item prop="type" label="监测项" class="postInfo-container-item">
              <el-input v-model="formData.typeName" disabled style="width:200px;"/>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="测点编号" prop="deviceId" class="postInfo-container-item">
              <el-input v-model="formData.deviceName" disabled style="width:200px;"/>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="3">
            <table class="form-table" v-for="(item,index) in alarmNum" :key="item" :label="item" :title="item">
              <tr class="title">
                <td colspan="3">{{item}}数据上下限值配置</td>
              </tr>
              <tr>
                <td>
                  <el-form-item label="数据上限:" class="postInfo-container-item">
                    <el-input-number :precision="2" :step="0.1" v-model="alarmParams[index].maxVal" style="width:200px;"
                                     placeholder="请输入数据上限" @change="changeVal"/>
                  </el-form-item>
                </td>
                <td>
                  <el-form-item label="数据下限:" class="postInfo-container-item">
                    <el-input-number :precision="2" :step="0.1" v-model="alarmParams[index].minVal" style="width:200px;"
                                     placeholder="请输入数据下限" @change="changeVal"/>
                  </el-form-item>
                </td>
          <td></td>
              </tr>
            </table>
          </td>
        </tr>
        <!--<td>
          <el-form-item v-if="isShow" label="选择参数" prop="choose">
            <el-select
              v-model="formData.choose"
              placeholder="请选择"
              clearable
              filterable
              :disabled="!formData.isAdd"
              style="width: 200px;"
            >
              <el-option
                v-for="item in chooseList"
                :key="item.name"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
        </td>
        <td></td>-->

      </table>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import {genUUID} from '@/utils/th_utils.js'
import {getDeviceTypeByProjectId} from '@/api/online/online-api'
import {
    saveAbonrmalDat
} from '@/api/alarming/abnormal-api.js'
import { getAbnormalDataList, getDataDeviceManagement } from '@/api/device/deviceManagement-api'
import {getProjectListWithAuth} from "@/api/project/project-api";

export default {
    name: 'TableEdit',
    props: {},
    data() {
        return {
          projectList:[],
            alarmNum: [''],
            alarmParams: [{}, {}, {}],
            deviceTypes: [],
            deviceList: [],
            title: '',
            chooseList: [],
            minVal: [],
            maxVal: [],
            dialogFormVisible: false,
            formloading: false,
            formData: {},
            rules: {
                type: [
                    {required: true, message: '请选择设备类型', trigger: 'blur'},
                ],
                deviceId: [
                    {required: true, message: '请选择设备', trigger: 'blur'},
                ],
                maxVal: [
                    {required: true, message: '数据上限为必填', trigger: 'blur'},
                ],
                minVal: [
                    {required: true, message: '数据下限为必填', trigger: 'blur'},
                ]
            },
            isShow: false
        }
    },
    mounted() {
      this.getProjectList()
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      changeVal() {
        this.$forceUpdate(); // 解决点击计数器失效问题（数据层级过深，导致视图无法刷新
      },
        // 初始化表单
        initForm() {
            this.formData = {
                id: genUUID(),
                projectId: '',
                projectName: '',
                deviceId: '',
                zoneValue: '',
                maxVal: '',
                deviceName: '',
                minVal: '',
                type: '',
                isAdd: true
            }
        },
        changeType() {
            this.formData.deviceId = ''
            this.getDeviceByProjectIdAndDeviceType()
            this.getAlarmNum()
        },
        async getDeviceByProjectIdAndDeviceType() {
            const params = {}
            params.type = this.formData.type
            params.projectId = this.formData.projectId
            await getAbnormalDataList(params).then((res) => {
                this.deviceList = res.result
            })
        },
        getAlarmNum() {
            if (this.formData.type === 'gnss') {
                this.alarmNum = ['X轴', 'Y轴', 'Z轴']
            } else if (this.formData.type === 'incline') {
                this.alarmNum = ['X轴', 'Y轴']
            } else if (this.formData.type === 'humid') {
                this.alarmNum = ['温度', '湿度']
            }  else if (this.formData.type === 'survey') {
                this.alarmNum = ['X轴', 'Y轴']
            }   else if (this.formData.type === 'wind') {
                this.alarmNum = ['风速', '风向']
            } else {
                this.alarmNum = ['']
            }
        },
        async showEdit(row) {
            await this.getDeviceType()
            if (!row) {
                this.title = '新增异常数据配置'
                this.initForm()
            } else {
                this.title = '编辑异常数据配置'
                this.formData = Object.assign({isAdd: false,}, row)
          console.info(this.formData)
                this.getProjectList()
                this.getDeviceType()
                await this.getDeviceByProjectIdAndDeviceType()
                this.getAlarmNum()
                this.splitAlarmParamsFormData()
            }
            this.dialogFormVisible = true
            this.$nextTick(() => {
                this.$refs['dataForm'].clearValidate()
            })
        },
        // 获取桥对应的所有设备类型
        async getDeviceType() {
            getDeviceTypeByProjectId({projectId: this.formData.projectId})
                .then((res) => {
                    this.deviceTypes = res.result
                })
                .catch(() => {
                })
        },
        getDeviceByDeviceId() {
            let device = ''
            this.deviceList.map(item => {
                if (item.id === this.formData.deviceId) {
                    device = item
                }
            })
            return device
        },
        /**
         * 编辑回填input拆分预警数据
         */
        splitAlarmParamsFormData() {
            this.alarmNum.forEach((item, i) => {
                this.alarmParams[i].maxVal = this.formData.maxVal ? this.formData.maxVal.split(',')[i] !== '/' ? this.formData.maxVal.split(',')[i] : undefined : ''
                this.alarmParams[i].minVal = this.formData.minVal ? this.formData.minVal.split(',')[i] !== '/' ? this.formData.minVal.split(',')[i] : undefined : ''
            })
        },
        /**
         * 新增重新拼装formData
         */
        concatFormData() {
            const device = this.getDeviceByDeviceId()
            this.formData.deviceName = device.name
            //this.formData.projectId = device.projectId
            this.formData.projectName = device.projectName
            this.formData.maxVal = ''
            this.formData.minVal = ''
            this.formData.zoneValue = ''
            this.alarmNum.forEach((item, i) => {
                this.formData.maxVal += (this.alarmParams[i].maxVal ? this.alarmParams[i].maxVal : '/') + ','
                this.formData.minVal += (this.alarmParams[i].minVal ? this.alarmParams[i].minVal : '/') + ','
                // this.formData.zoneValue += '(-∞,' + this.alarmParams[i].minVal + '),(' + this.alarmParams[i].maxVal + ',+∞)' + '/'
              this.minVal[i] = this.alarmParams[i].minVal == undefined ? '/' : '(-∞,' + this.alarmParams[i].minVal + ')'
              this.maxVal[i] = this.alarmParams[i].maxVal == undefined ? '/' : '(' + this.alarmParams[i].maxVal + ',+∞)'
              this.formData.zoneValue += this.minVal[i] + ',' + this.maxVal[i] + '   '
            })
            this.formData.maxVal = this.formData.maxVal.substring(0, this.formData.maxVal.length - 1)
            this.formData.minVal = this.formData.minVal.substring(0, this.formData.minVal.length - 1)
            this.formData.zoneValue = this.formData.zoneValue.substring(0, this.formData.zoneValue.length - 1)
        },
        save() {
            this.$refs['dataForm'].validate((valid) => {
                if (valid) {
                  for (const item of this.alarmParams) {
                    if (item.minVal > item.maxVal) {
                      this.$message.error('下限值不得大于上限值!')
                      return
                    }
                  }
                    this.concatFormData()
                    saveAbonrmalDat(this.formData).then(response => {
                        this.$baseMessage(
                            this.formData.isAdd ? '新增异常数据配置成功！' : '修改异常数据配置成功!',
                            'success', 'vab-hey-message-success'
                        )
                        this.close()
                        this.$emit('fetch-data')
                    }).catch(err => {
                        this.$baseMessage(
                            this.formData.isAdd ? '新增异常数据配置失败！' : '修改异常数据配置失败!',
                            'error', 'vab-hey-message-error'
                        )
                    })
                }
            })
        },
        close() {
            this.$refs['dataForm'].resetFields()
            this.isShow = false
            this.deviceList = []
            this.alarmNum = ['']
            this.alarmParams = [{}, {}, {}]
            this.formData = this.$options.data().formData
            this.dialogFormVisible = false
        },
    },
}
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
  ::v-deep table.form-table {
    .el-form-item {
      justify-content: center;
    }
    .el-form-item__label {
      border-left: 1px solid #bbbbbb;
    }
    .el-form-item__content {
      width: 210px;
    }
  }
</style>
