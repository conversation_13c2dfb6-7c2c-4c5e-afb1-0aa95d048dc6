<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1050px"
    @close="close"
    top="5vh"
    center
    v-drag
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      :rules="rules"
      :model="formData"
      label-position="right"
      label-width="130px"
    >
      <table v-loading="formloading" class="form-table" >
        <tr class="title"><td colspan="2">基本信息</td></tr>
        <tr>
          <td>
            <el-form-item label="用户名" prop="userName">
              <el-input v-model="formData.userName" style="width:280px;"/>
            </el-form-item>
          </td>
          <td>
            <el-form-item label="所在公司" prop="company">
              <el-input v-model="formData.company" style="width:280px;"/>
            </el-form-item>
          </td>
        </tr>
        <tr>
          <td colspan="2">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" style="width:280px;"/>
            </el-form-item>
          </td>
        </tr>
      </table>

    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
import { genUUID } from '@/utils/th_utils.js'
import { parseTime } from '@/utils/index'
import { mapActions, mapGetters } from 'vuex'
import { saveAlertConfig} from '@/api/alarming/alertConfig-api.js'
export default {
  name: 'emailConfigEdit',
  props:{},
  data() {
    const validateEmail = (rule, value, callback) => {
      const regA = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
      if (value && !regA.test(value)) {
        callback(new Error('邮箱格式不对'))
      } else {
        callback()
      }
    }

    return {
      title: '',
      dialogFormVisible: false,
      formloading: false,
      formData: {},
      rules: {
        email:[
          { required: true, message: '邮件为必填', trigger: 'blur' },
          {validator: validateEmail, trigger: 'change' }
        ]
      },
    }
  },
  computed: {
    ...mapGetters({
      userid: 'user/userid',
    }),
  },
  created() {

  },
  mounted() {
  },
  methods: {
    showEdit(row,projectId) {
      if (!row) {
        this.title = '新增中断邮件预警配置'
        this.initForm(projectId)
      } else {
        this.title = '编辑中断邮件预警配置'
        this.formData = Object.assign({ isAdd: false,}, row)
      }
      this.dialogFormVisible = true
      this.$nextTick(()=>{
        this.$refs['dataForm'].clearValidate()
      })
    },
    // 初始化表单
    initForm(projectId) {
      this.formData = {
        id: genUUID(),
        userName: '',
        projectId:projectId,
        company: '',
        level: '',
        email: '',
        sort:'1',
        isAdd: true,
        type: '1', // type值为1代表邮件预警配置
      }
    },
    save() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          saveAlertConfig(this.formData).then(response => {
            this.$baseMessage(
              this.formData.isAdd ? '新增预警配置成功！' : '修改预警配置成功!',
              'success', 'vab-hey-message-success'
            )
            this.close()
            this.$emit('fetch-data')
          }).catch(err => {
            this.$baseMessage(
              this.formData.isAdd ? '新增预警配置失败！' : '修改预警配置失败!' ,
              'error', 'vab-hey-message-error'
            )
          })
        }
      })
    },
    close() {
      this.$refs['dataForm'].resetFields()
      this.formData = this.$options.data().formData
      this.dialogFormVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dropTree{
  width: 100%;
  max-height:300px;
  overflow:auto;
}
</style>
