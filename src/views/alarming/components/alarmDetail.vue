<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    title="预警处理详情"
    :visible.sync="dialogDetailVisible"
    width="1250px"
    top="5vh"
    center
    v-drag
  >
        <!-- <div class="form-table-title">基本信息</div> -->
        <el-descriptions class="margin-top" :column="3" border size="medium">
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{formData.projectName}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">设备编号</template>
            {{formData.deviceCode}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">设备名称</template>
            {{formData.deviceName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">设备类型</template>
            {{formData.deviceTypeName}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">预警类型</template>
            {{formData.alarmTypeName}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">预警等级</template>
            {{formData.alarmLevel}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">预警数值</template>
            {{formData.alarmData}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">采集时间</template>
            {{formData.prodTime}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">采集值</template>
            {{formData.val}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">状态</template>
            <el-tag v-if="formData.status === '-1'" type="primary">已发送(业主)</el-tag>
            <el-tag v-if="formData.status === '0'" type="danger">待处理</el-tag>
            <el-tag v-if="formData.status === '1'" type="success">已处理(忽略)</el-tag>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">处理人</template>
            {{formData.updateName}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">处理时间</template>
            {{formData.updateTime}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">处理备注</template>
            {{formData.remark}}
          </el-descriptions-item>
        </el-descriptions>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="dialogDetailVisible = false">
        关闭
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { parseTime } from '@/utils'
  export default {
    props: {
        default() {
          return []
        }

    },
    components: {

    },
    data() {
      return {
        dialogDetailVisible: false,
        formData: {},
      }
    },
    methods: {
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      async showDialog(row) {
        this.formData = { ...row }
        this.dialogDetailVisible=true
      },
    }
  }
</script>

<style lang="scss" scoped>
</style>
