<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label="项目选择" prop="projectId">
            <el-select v-model="queryForm.projectId" filterable size="medium" @change="getDeviceType">
              <el-option
                v-for="item in projectList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="监测项" prop="deviceType" >
            <el-select v-model="queryForm.deviceType" placeholder="请选择监测项" style="width:200px;" clearable>
              <el-option v-for="item in deviceTypes" :key="item.type" :label="item.typeName" :value="item.type" />
            </el-select>
          </el-form-item>
          <el-form-item label="处理状态" prop="status">
          <el-select v-model="queryForm.status" clearable placeholder="请选择">
          <el-option label="已发送(业主)" value="-1" />
          <el-option label="待处理" value="0" />
           <el-option label="已处理(忽略)" value="1" />
          </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
        <el-button icon="el-icon-edit" type="primary" @click="editStatus">
          一键处理
        </el-button>
        <el-button icon="el-icon-delete" type="danger" @click="delBatch">
          批删
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
      <el-table-column align="center" type="selection" width="55" />
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{ row }">
              <span v-if="item.label === '状态'">
               <el-tag v-if="row.status === '-1'" type="primary">已发送(业主)</el-tag>
               <el-tag v-if="row.status === '0'" type="danger">待处理</el-tag>
               <el-tag v-if="row.status === '1'" type="success">已处理(忽略)</el-tag>
              </span>

              <span v-else>{{ row[item.prop] }}</span>
            </template>

      </el-table-column>

      <el-table-column align="center" label="操作" width="320">
        <template #default="{ row }">
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row) "
          >
          预警处理
          </el-button>
          <el-button
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
          >
            删除
          </el-button>
          <el-button icon="el-icon-view" style="margin: 0 10px 0 0 !important" type="success"
            @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <table-edit ref="edit" @fetch-data="fetchData" />
     <alarmDetail ref="alarmDetail" />
  </div>
</template>

<script>
  import { getDictList } from '@/api/system/dict-api'
  import {  getAlarmDataByPage,deleteAlarmData,saveAlarmData,updateAlarmData} from '@/api/alarming/alarmData-api.js'
  import TableEdit from './components/AlarmDataEdit'
  import alarmDetail from './components/alarmDetail.vue'
  import {getDeviceTypeByProjectId} from '@/api/online/online-api'
  import tableMix from '@/views/mixins/table'
  import { parseTime } from '@/utils'
  import store from '@/store'
  import {getProjectListWithAuth} from "@/api/project/project-api";
  export default {
    name: 'alarmData',
    components: {
      TableEdit,alarmDetail
    },
    mixins: [tableMix],
    data() {
      return {
        selectDeviceType: [],
        deviceTypes: [],
        selectAlertStatus: [],
        queryData: {
          deviceType: ''
        },
        checkList: ['项目名称','监测项','设备编号', '测点编号','预警类型', '预警等级','预警数值','采集时间','采集值','状态','处理人','处理时间'],
        columns: [
          {
            label: '项目名称',
            prop: 'projectName',
            disableCheck: true,
          },
          {
            label: '监测项',
            prop: 'deviceTypeName',
            disableCheck: true
          },
          {
            label: '设备编号',
            prop: 'deviceCode',
            disableCheck: true,
          },
          {
            label: '测点编号',
            prop: 'deviceName',
          },
          {
            label: '预警类型',
            prop: 'alarmTypeName',
          },
          {
            label: '预警等级',
            prop: 'alarmLevel',
          },
          {
            label: '预警数值',
            prop: 'alarmData',
          },
          {
            label: '采集时间',
            prop: 'prodTime',
          },
          {
            label: '采集值',
            prop: 'val',
          },
          {
            label: '状态',
            prop: 'status',
          },
          {
            label: '处理人',
            prop: 'updateName',
          },
          {
            label: '处理时间',
            prop: 'updateTime',
          },
        ],
        projectId:'',
        queryForm: {
          projectId:'',
          dtuNo:'',
          name:'',
          deviceType: ''
        },
        projectList:[]
      }
    },
    computed: {
      finallyColumns() {
        return this.columns.filter((item) =>
          this.checkList.includes(item.label)
        )
      },
    },
    created() {
      this.projectId = sessionStorage.getItem('def_project_Id')
      this.fetchData()
      this.getProjectList()
    },
    mounted() {
      this.getDictDetails()
    },
    methods: {
      getProjectList() {
        getProjectListWithAuth({}).then((res) => {
          this.projectList = res.result || []
        })
      },
      getDeviceType() {
          getDeviceTypeByProjectId({projectId: this.queryForm.projectId})
              .then((res) => {
                  this.deviceTypes = res.result
              })
              .catch(() => {
              })
      },
      backDepart(list){
        return list.map(item=> item.departName).join(',')
      },
      backRolelist(list){
        return list.map(item=> item.roleName).join(',')
      },
      handleAdd() {
        this.$refs['edit'].showEdit()
      },
      handleEdit(row) {
        this.$refs['edit'].showEdit(row)
      },
      handleDetail(row) {
        this.$refs['alarmDetail'].showDialog(row)
      },
      handleDelete(row) {
        if (row.id) {
          this.$baseConfirm('你确定要删除当前数据吗', null, async () => {
            deleteAlarmData({id:row.id}).then(() => {
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('删除成功!','success','vab-hey-message-success')
            })
            .catch(() => {
              this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
            })
          })
        }
      },

      getDictDetails() {
        getDictList({ dictCode: 'deviceType' }).then((res) => {
          const result = res.result[0].children
          this.selectDeviceType = result.map(item => {
            return {
              label: item.dictName,
              value: item.dictCode
            }
          })
        })
        getDictList({ dictCode: 'alertStatus' }).then((res) => {
          const result = res.result[0].children
          this.selectAlertStatus = result.map(item => {
            return {
              label: item.dictName,
              value: item.dictCode
            }
          })
        })
      },
      delBatch() {
        if (this.selectRows.length == 0) {
          this.$baseMessage('请最少选中一条记录!', 'error', 'vab-hey-message-error')
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量删除数据吗', null, async () => {
            deleteAlarmData({id:ids}).then(response=>{
              this.pageInfo.curPage = 1
              this.fetchData()
              this.$baseMessage('批量删除成功！', 'success', 'vab-hey-message-success')
            }).catch(err=>{
              this.$baseMessage('批量删除失败！', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      editStatus() {
        if (this.selectRows.length == 0) {
          this.$baseMessage(
            '请选中最少一条记录!!!',
            'error',
            'vab-hey-message-error'
          )
        } else {
          const ids = this.selectRows.map((item) => item.id).join(',')
          this.$baseConfirm('你确定要批量修改状态为已处理吗', null, async () => {
            await updateAlarmData({id:ids,status:'1',
            updateBy:this.$store.getters['user/userid'],
            updateByName:this.$store.getters['user/username'],
            updateTime:parseTime(new Date())}).then(response=>{
              this.fetchData()
              this.$baseMessage('批量修改状态成功！', 'success', 'vab-hey-message-success')
            }).catch(err=>{
              this.$baseMessage('批量修改状态失败！', 'error', 'vab-hey-message-error')
            })
          })
        }
      },
      async fetchData() {
        this.queryForm.curPage = this.pageInfo.curPage
        this.queryForm.pageSize = this.pageInfo.pageSize
        this.listLoading = true
        const { result: { records, total }} = await getAlarmDataByPage(this.queryForm)
        this.list = records
        this.pageInfo.total = Number(total)
        this.listLoading = false
      },
    },
  }
</script>
<style lang="scss" scoped>

</style>
