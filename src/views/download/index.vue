<template>
  <div>
    <div v-if="WeiXin" class="openbox">
      <img style="float: right;" src="../../assets/appDownload/downopen.png" alt=""width="80%" />
    </div>
    <div class="login-container">
      <p class="p1">桥梁监测管理APP</p>
      <p class="p2">给你<span>全新体验</span></p>
      <img style="margin-top: 10px;" src="../../assets/appDownload/downimg.png" alt="" width="100%"/>
      <img style="position:absolute; bottom: 0px;left: 0px;display: block;" src="../../assets/appDownload/downbg.png" alt="" width="100%"/>
      <a class="downBtn" :href="apkUrl" :download="apkUrl"></a>
    </div>
  </div>
</template>

<script>
  import { appFileAddr } from '@/utils/constants'
  import { getAppVersionData } from '@/api/system/syAppVersion-api'
  export default {
    name: 'download',
    components: { },
    data() {
      return {
        apkUrl: '',
        WeiXin: true
      }
    },

    mounted() {
      this.getApkUrl()
    },

    methods: {
      isWeiXin(){
        //window.navigator.userAgent属性包含了浏览器类型、版本、操作系统类型、浏览器引擎类型等信息，这个属性可以用来判断浏览器类型
        var ua = window.navigator.userAgent.toLowerCase()
        //通过正则表达式匹配ua中是否含有MicroMessenger字符串
        if(ua.match(/MicroMessenger/i) == 'micromessenger'){
          return true
        }else{
          return false
        }
      },
      getApkUrl() {
        this.WeiXin = this.isWeiXin()
        let params = {
        	type: 'apk'
        }
        getAppVersionData(params).then(response=>{
          const appRes = response.data.result
          this.apkUrl = appFileAddr + appRes.path
        })
      },

    }
  }
</script>

<style lang="scss" scoped>
  .login-container{
    font-family: '微软雅黑';
    position: relative;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    overflow: hidden;
  }
  .openbox{
    width: 100vw;
    height: 100vh;
    position: fixed;
    z-index: 9;
    background: rgba($color: #000000, $alpha: 0.8);
  }
  .downBtn{
    width: 267px;
    height: 56px;
    position: fixed;
    z-index: 1;
    bottom: 10%;
    left: 50%;
    margin-left: -134px;
    background-image: url(../../assets/appDownload/downbtn.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  .p1{
    margin: 30px 0px 10px 0px;
    text-align: center;
    font-size: 30px;
    color: #584400;
    font-weight: bold;
  }
  .p2{
    margin: 10px;
    padding-left: 30%;
    font-size: 30px;
    color: #584400;
    font-weight: bold;

    span{
      color: #0152d5;
    }
  }
</style>
