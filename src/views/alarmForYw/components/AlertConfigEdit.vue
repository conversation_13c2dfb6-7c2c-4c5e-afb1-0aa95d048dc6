<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1050px"
    @close="close"
    center
    v-drag
  >
 <el-form
     ref="dataForm"
     v-loading="formloading"
     :inline="true"
     :rules="rules"
     :model="formData"
     label-position="right"
     label-width="100px"
   >
   <table v-loading="formloading" class="form-table" >
     <tr class="title"><td colspan="3">基本信息</td></tr>
     <tr>
         <td>
             <el-form-item label="用户名" prop="userName">
               <el-input v-model="formData.userName" style="width:200px;"/>
             </el-form-item>
         </td>
       <td>
         <el-form-item label="邮箱" prop="email">
           <el-input v-model="formData.email" style="width:200px;"/>
         </el-form-item>
       </td>
      </tr>
      <tr><td>
          <el-form-item label="所在公司" prop="company">
            <el-input v-model="formData.company" style="width:200px;"/>
          </el-form-item>
        </td>
        </tr>
     <tr>

     </tr>
      </table>

      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils.js'
  import { parseTime } from '@/utils/index'
  import { mapActions, mapGetters } from 'vuex'
  import { saveAlertConfig} from '@/api/alarming/alertConfig-api.js'
  export default {
    name: 'TableEdit',
    props:{},
    data() {
      const validateReg = (rule, value, callback) => {
      const reg = /^1\d{10}$/
      if (value && !reg.test(value)) {
      callback(new Error('电话格式不对'))
      } else {
      callback()
      }
      }
      const validateEmail = (rule, value, callback) => {
      const regA = /^([a-zA-Z]|[0-9])(\w|\-)+@[a-zA-Z0-9]+\.([a-zA-Z]{2,4})$/
      if (value && !regA.test(value)) {
      callback(new Error('邮箱格式不对'))
      } else {
      callback()
      }
      }

      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        levelList: [
          {label: '1',value:'1'},
          {label: '2',value:'2'},
          {label: '3',value:'3'},
          {label: '4',value:'4'}
        ],
        rules: {
          phone:[
           {validator: validateReg, trigger: 'change' }
          ],
          email:{validator: validateEmail, trigger: 'change' },
        },
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
      }),
    },
    created() {

    },
    mounted() {
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增运维预警配置'
          this.initForm()
        } else {
          this.title = '编辑运维预警配置'
          this.formData = Object.assign({ isAdd: false,}, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          userName: '',
          projectId:'all',
          phone: '',
          level: '',
          email: '',
          sort:'1',
          isAdd: true,
          type: '', // type值为0代表预警配置
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveAlertConfig(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ? '新增运维预警配置成功！' : '修改运维预警配置成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ? '新增运维预警配置失败！' : '修改运维预警配置失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
  ::v-deep table.form-table {
    .el-form-item {
      justify-content: center;
    }
    .el-form-item__label {
      background-color: #fff;
      font-weight: bold;
      border: none;
    }
    .el-form-item__content {
      width: 210px;
      padding: 10px 0 10px 10px;
    }
  }
</style>
