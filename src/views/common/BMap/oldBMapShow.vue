<template>
  <el-dialog
    :title="title"
    :visible.sync="isShowDialog"
    width="1200px"
    :close-on-click-modal="false"
    top="5vh"
    center
    :close="diaogClose"
    append-to-body
  >
  <div class="mapBox">
    <div id="mapGLBox"></div>
  </div>
  <div slot="footer" class="dialog-footer">
    <el-button type="danger" @click="diaogClose">
      关闭
    </el-button>
  </div>
  </el-dialog>
</template>

<script>

export default{
  name:"BaiduMapShow",
  components:{},
  data() {
    return {
      title: '位置',
      isShowDialog: false,

      zoom: 12,//地图展示级别
    }
  },
  beforeDestroy() {
    if (this.map) {
      this.map.clearOverlays()
      this.map = null; // 可选，帮助垃圾回收
    }
    // this.map.clearOverlays()
    // this.map = null
  },
  methods: {
    diaogClose(){
      this.showBMap=false
      this.isShowDialog = false
    },
    showBaiDuMap(data){
      this.isShowDialog = true
      this.$nextTick(() => {
        this.createrMarker(data)
      })
    },
    createrMarker(data) {
      this.map = new BMapGL.Map('mapGLBox', {
        enableMapClick: false,
        minZoom: 5,
        preserveDrawingBuffer: true
      })
      const point = new BMapGL.Point(data.position.lng, data.position.lat)
      this.map.centerAndZoom(point, 12)
      this.map.enableScrollWheelZoom(true)
      let marker = new BMapGL.Marker(point);
      this.map.addOverlay(marker);
      let opts = {
        width: 280,
        title: '项目信息'
      }
      var infoWindow = new BMapGL.InfoWindow(data.info, opts);  // 创建信息窗口对象
      this.map.openInfoWindow(infoWindow, point);
    }
  },
}
</script>

<style lang="scss" scoped>
  .mapBox{
    width: 1150px;
    height: 600px;
  }
  #mapGLBox {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
