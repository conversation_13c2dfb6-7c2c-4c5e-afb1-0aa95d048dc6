<template>
  <el-dialog
    :title="title"
    :visible.sync="isShowDialog"
    width="1200px"
    :close-on-click-modal="false"
    top="5vh"
    center
    :close="diaogClose"
    append-to-body
  >
  <div class="mapBox">
    <div id="mapTBox"></div>
  </div>
  <div slot="footer" class="dialog-footer">
    <el-button type="danger" @click="diaogClose">
      关闭
    </el-button>
  </div>
  </el-dialog>
</template>

<script>

export default{
  name:"BaiduMapShow",
  components:{},
  data() {
    return {
      title: '位置',
      isShowDialog: false,

      zoom: 12,//地图展示级别
    }
  },
  beforeDestroy() {
    if (this.map) {
      this.map.clearOverlays()
      this.map = null; // 可选，帮助垃圾回收
    }
    // this.map.clearOverlays()
    // this.map = null
  },
  methods: {
    diaogClose(){
      this.showBMap=false
      this.isShowDialog = false
    },
    showBaiDuMap(data){
      this.isShowDialog = true
      this.$nextTick(() => {
        this.createrMarker(data)
      })
    },
    createrMarker(data) {
      this.map = new T.Map('mapTBox', {
        projection: 'EPSG:4326',
        minZoom: 5,
      })
      const point = new T.LngLat(data.position.lng, data.position.lat)
      this.map.centerAndZoom(point, 12)

      var ctrl = new T.Control.MapType();
      this.map.addControl(ctrl);
      this.map.setMapType(TMAP_HYBRID_MAP)

      let marker = new T.Marker(point);
      this.map.addOverLay(marker);

      let opts = {
        width: 280,
        title: '项目信息'
      }
      var infoWindow = new T.InfoWindow(opts);
      infoWindow.setContent(data.info);
      this.map.openInfoWindow(infoWindow, point);
    }
  },
}
</script>

<style>
.tdt-control-copyright {
  display: none;
}
</style>

<style lang="scss" scoped>
  .mapBox{
    width: 1150px;
    height: 600px;
  }
  #mapTBox {
    position: relative;
    width: 100%;
    height: 100%;
  }
</style>
