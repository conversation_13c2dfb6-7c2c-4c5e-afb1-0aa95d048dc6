<template>
  <el-dialog
    v-drag
    append-to-body
    title="数据补录"
    :visible.sync="dialogFormVisible"
    width="900px"
    top="10vh"
    center
    @close="close"
    :close-on-click-modal="false"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label-width="80px" label="设备类型:" class="postInfo-container-item">
            <el-select v-model="queryForm.type" placeholder="选择设备类型" style="width:200px;" @change="getDeviceList">
              <el-option v-for="item in deviceTypes" :key="item.id" :label="item.label" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label-width="80px" label="设备名称:" class="postInfo-container-item">
            <el-select v-model="queryForm.deviceId" placeholder="选择设备类型" style="width:200px;">
              <el-option v-for="item in deviceList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-plus" size="small" @click="showImportDialog">补录数据</el-button>
          </el-form-item>
          <el-form-item>
            <el-button
              type="success"
              icon="el-icon-download"
              size="small"
              :loading="tempBtn"
              :disabled="tempBtn"
              @click="downloadFile"
            >{{deviceTypeName + tempDownText}}
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
    </vab-query-form>
    <ImportDataPopup ref="ImportDataPopup" />
  </el-dialog>
</template>

<script>
import { getDeviceTypeByProjectId } from '@/api/online/online-api'
import { getDataDeviceManagement } from '@/api/device/deviceManagement-api.js'
import ImportDataPopup from '@/views/online/components/importDataPopup'
import {downloadFile, downLoadFileMinio} from '@/api/system/uploadFile-api'

export default {
  name: 'additionalDataEntry',
  components: { ImportDataPopup },
  data() {
    return {
      dialogFormVisible: false,
      tempBtn: false,
      tempDownText: '模板下载',
      deviceTypeName: '',
      listloading: false,
      deviceTypes: [],
      deviceList: [],
      queryForm: {
        type: '',
        projectId: sessionStorage.getItem('def_project_Id'),
        deviceId: ''
      },
      devicesTypeMap: {
        'strain': '应变',
        'humid': '温湿度',
        'floor': '静力水准',
        'displacement': '伸缩缝',
        'optical_strain': '光纤应变',
        'rifts': '裂缝',
        'soly': '索力',
        'vibrate': '振动加速度',
        'incline': '倾角仪',
        'gnss': 'GNSS位移',
        'wind': '风速风向',
        'floor_d': '动挠度',
        'survey': '深层水平位移',
        'brilevel': '激光位移',
        'rainfall':'雨量' ,
        'water': '地下水位',
        'strain_d': '动应变',
        'earthPress': '土压力',
        'flow': '流量流速',
        'pressure': '浸润线',
        'ph': '酸碱度',
        'capacity': '电池容量',
        'voltage': '电压',
        'resistance': '内阻',
        'water_quality': '水质',
        'vibrate_spectrum': '振动频谱'
      },
      downloadFileId: ['template_floor','template_incline','template_gnss','template_humid','template_virbrate','template_wind']
    }
  },
  created() {
    this.getDeviceType()
  },
  methods: {
    close() {
      this.queryForm = {
        type: '',
        projectId: sessionStorage.getItem('def_project_Id'),
        deviceId: ''
      },
      this.dialogFormVisible = false
    },
    showDialog() {
      this.dialogFormVisible = true
    },
    // 获取桥对应的所有设备类型
    getDeviceType() {
      this.listloading = true
      const projectId = sessionStorage.getItem('def_project_Id')
      getDeviceTypeByProjectId({projectId: projectId})
        .then((res) => {
          this.listloading = false
          for (const data of res.result) {
            const treeNode = {}
            treeNode.id = data.type
            treeNode.label = data.typeName
            treeNode.disabled = false
            this.deviceTypes.push({...data,...treeNode})
          }
        })
        .catch(() => {
        })
    },
    getDeviceList() {
      this.deviceList.length = 0
      this.queryForm.deviceId = ''
      this.deviceTypeName = this.devicesTypeMap[this.queryForm.type]
      getDataDeviceManagement({projectId: this.queryForm.projectId, type: this.queryForm.type})
        .then(async (res) => {
          for (const data of res.result) {
            const treeNode = {}
            treeNode.id = data.id
            treeNode.label = data.code
            treeNode.disabled = false
            this.deviceList.push({...data,...treeNode})
          }
        })
        .catch(() => {
        })
    },
    showImportDialog() {
      if (this.queryForm.type === '') {
        this.$baseMessage('请先选择需要补录的设备类型！', 'error', 'vab-hey-message-error')
      } if (this.queryForm.deviceId === '') {
        this.$baseMessage('请先选择需要补录的设备！', 'error', 'vab-hey-message-error')
      } else {
        this.$refs.ImportDataPopup.showImportDialog(this.queryForm)
      }

    },
    downloadFile() {
      if (this.deviceTypeName === '') {
        this.$baseMessage('请先选择需要补录的设备类型！', 'error', 'vab-hey-message-error')
        return
      }
      this.tempBtn = true
      this.tempDownText = '模板下载中'
      let fileId = ''
      if (this.queryForm.type === 'brilevel' || this.queryForm.type === 'capacity' || this.queryForm.type === 'displacement'
        || this.queryForm.type === 'earthPress' || this.queryForm.type === 'floor' || this.queryForm.type === 'flow'
        || this.queryForm.type === 'optical_strain' || this.queryForm.type === 'ph' || this.queryForm.type === 'pressure'
        || this.queryForm.type === 'rainfall' || this.queryForm.type === 'resistance' || this.queryForm.type === 'rifts'
        || this.queryForm.type === 'soly' || this.queryForm.type === 'strain' || this.queryForm.type === 'strain_d'
        || this.queryForm.type === 'vibrate' || this.queryForm.type === 'voltage' || this.queryForm.type === 'water'
        || this.queryForm.type === 'water_quality') {
        fileId = this.downloadFileId[0]
      } else if(this.queryForm.type === 'floor_d' || this.queryForm.type === 'incline' || this.queryForm.type === 'survey') {
        fileId = this.downloadFileId[1]
      } else if(this.queryForm.type === 'gnss') {
        fileId = this.downloadFileId[2]
      } else if(this.queryForm.type === 'humid') {
        fileId = this.downloadFileId[3]
      } else if(this.queryForm.type === 'vibrate_spectrum') {
        fileId = this.downloadFileId[4]
      } else if(this.queryForm.type === 'wind') {
        fileId = this.downloadFileId[5]
      } else {
        this.$baseMessage('不存在该类型的模板，请联系管理员！', 'error', 'vab-hey-message-error')
        return
      }
      downLoadFileMinio(fileId).then(response => {
        const data = response
        if (!data) {
          return
        }
        const url = window.URL.createObjectURL(new Blob([data]))
        const a = document.createElement('a')
        a.style.display = 'none'
        a.href = url
        a.setAttribute('download', this.deviceTypeName + '数据补录信息表.xlsx')
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        window.URL.revokeObjectURL(url)
        this.tempBtn = false
        this.tempDownText = '模板下载'
      }).catch(() => {
        this.tempBtn = false
        this.tempDownText = '模板下载'
      })
    }
  }
}
</script>

<style scoped>

</style>
