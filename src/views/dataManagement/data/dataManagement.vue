<template>
  <div class="index-container">
    <el-row :gutter="20">
      <!-- 左侧侧边栏 -->
      <el-col :lg="4" :md="8" :sm="24" :xl="4" :xs="24">
        <infrastructure-projects  ref="projectTypeSelector" @change="handleProjectTypeChange" />
        <el-card shadow="hover" body-style="height:30vh;overflow:auto;">
          <div slot="header" class="clearfix">
            <span>项目状态</span>
          </div>
          <div
            v-for="item in statusList"
            :key="item.alarmLevel"
            class="status-item"
            :class="{ active: selectedStatus === item.alarmLevel }"
            @click="handleStatusChange(item.alarmLevel)"
          >
            {{ item.label }}
            <span> {{ item.count }} </span>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧内容区 -->
      <el-col :lg="20" :md="16" :sm="24" :xl="20" :xs="24">
        <el-form :inline="true" class="search-bar">
          <el-form-item label="项目名称">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入项目名称"
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="20" v-loading="loading">
          <!-- 根据 projectData 是否有数据显示不同内容 -->
          <template v-if="projectData.length > 0">
            <el-col
              v-for="(item, index) in projectData"
              :key="index"
              :span="12"
            >
              <el-card class="project-card">
                <div class="card-content">
                  <img
                    :src="item.pic || noImageUrl"
                    class="project-image"
                    alt="项目图片"
                  >
                  <div class="project-info">
                    <h3 class="project-title">{{ item.name }}</h3>
                    <p class="project-desc">业主单位：{{ item.ownerName }}</p>
                    <p class="project-desc">介绍：{{ item.introduce }}</p>
                    <p class="project-desc">安装时间：{{ dateFormat(item.startTime) }}</p>
                    <p class="project-desc">地理坐标：{{ item.position }}
                      <el-link type="primary"  @click="showBMap(item)" v-if="item.position!='' && item.position!= null"><i  class="el-icon-map-location"></i></el-link></p>
                    <div class="project-footer">
                      <span class="project-type">项目类型：<strong>{{ getProjectType(item.projectType) }}</strong></span>
                     <span>项目状态： <el-tag :type="getAlarmTagType(item.alarmLevel)">
                       {{ getAlarmText(item.alarmLevel) }}
                      </el-tag></span>
                      <el-button
                        type="primary"
                        @click="handleDataManagement(item.id)"
                      >
                        数据管理
                      </el-button>
                    </div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </template>
          <template v-else>
            <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
          </template>
        </el-row>
      </el-col>
    </el-row>
<!--    <projectMonitor ref="projectMonitor"/>-->
    <BMapShow ref="BMapShow" />
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script>
  import InfrastructureProjects from "@/views/system/InfrastructureProjects"
  // import projectMonitor from "./components/projectMonitor"
  import {getProjectAndAlarm, getProjectCountForLevel, getProjectAndAlarmByPage} from '@/api/projectMap/index'
  import { getDictList } from '@/api/system/dict-api'
  import {parseTime} from "@/utils"
  import BMapShow from "@/views/common/BMap/BMapShow.vue";
  export default {
    name: 'ProjectDashboard',
    components: {
      BMapShow,
      InfrastructureProjects
    },
    data() {
      return {
        loading: false,
        noImageUrl: require('@/assets/empty_images/pic_empty.png'),
        statusList: [
          { alarmLevel: '', label: '全部', type: 'success' },
          { alarmLevel: '0', label: '正常', type: 'success' },
          { alarmLevel: '1', label: '红色预警', type: 'danger'},
          { alarmLevel: '2', label: '橙色预警', type: 'warning' },
          { alarmLevel: '3', label: '黄色预警', type: 'warning' },
          { alarmLevel: '4', label: '蓝色预警', type: 'info' }
        ],
        selectedStatus: '', // 默认选中正常状态
        projectData: [],
        projectTypes: [],
        queryParams: {
          projectType: '',
          alarmLevel: '',
          name: ''
        },
        pageInfo: {
          curPage: 1,
          pageSize: 10,
          total: 0
        },
        layout: 'total, sizes, prev, pager, next, jumper',
      }
    },
    created() {
     // this.fetchProjects()
      this.statusCount()
    },
    computed: {
      // 将 statusList 转换为键值对映射，例如 { '0': 'success', '1': 'danger', ... }
      alarmTypeMap() {
        return this.statusList.reduce((map, item) => {
          map[item.alarmLevel] = item.type;
          return map;
        }, {});
      }
    },
    methods: {
      handleSizeChange(val) {
        this.pageInfo.pageSize = val
        this.pageInfo.curPage = 1
        this.fetchProjects()
      },
      handleCurrentChange(val) {
        this.pageInfo.curPage = val
        this.fetchProjects()
      },
      async statusCount() {
        const res = await getProjectTypeSum({})
        const result = res.result
        const projectAllCount = result.reduce((sum, item) => {
          return sum + parseInt(item.projectCount || 0, 10)// 确保字符串转为数字
        }, 0)

        let count = 0
        await getProjectCountForLevel({}).then(result => {
          this.statusList.forEach(item => {
            const matchItem = result.result.find(item2 => Number(item.alarmLevel) === item2.alarmLevel);
            if (matchItem) {
              item.count = matchItem.projectNum
              count += Number(matchItem.projectNum)
            } else {
              item.count = 0;
            }
          });
        }).catch(error => {
          console.error('获取状态数量失败:', error);
        });
        const fullOption = {alarmLevel: '', label: '全部', type: 'success', count:projectAllCount }
        this.statusList.forEach(item => {
          if(item.alarmLevel===''){
            item.count = projectAllCount
          }else if(item.alarmLevel==='0'){
            item.count = projectAllCount - count
          }
        })
        this.selectedStatus = fullOption
        this.$emit('change', fullOption.alarmLevel)
      },
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
      // 预警等级显示
      getAlarmText(level) {
        const type = this.statusList.find(t => t.alarmLevel == level)
        return type?.label || '未知等级'
      },
     //类型获取方法
      getAlarmTagType(alarmLevel) {
        // 直接从映射中读取，未找到时返回 'success'
        return this.alarmTypeMap[String(alarmLevel)] || 'success';
      },

      // 加载项目类型
      async loadProjectTypes() {
        try {
          const { result } = await getDictList({ dictCode: 'projectType' })
          this.projectTypes = result[0]?.children || []
        } catch (error) {
          console.error('加载项目类型失败:', error)
        }
      },

      // 获取项目数据
      async fetchProjects() {
        this.loading = true
        this.loadProjectTypes()
        try {
          this.queryParams.curPage = this.pageInfo.curPage
          this.queryParams.pageSize = this.pageInfo.pageSize
          const { result: { records, total }} = await getProjectAndAlarmByPage(this.queryParams)
          this.projectData = records
          this.pageInfo.total = Number(total)
        } catch (error) {
          console.error('获取项目数据失败:', error)
        } finally {
          this.loading = false
        }
      },
      // 项目类型显示
      getProjectType(typeCode) {
        const type = this.projectTypes.find(t => t.dictCode == typeCode)
        return type?.dictName || '未知类型'
      },
      // 事件处理
      handleProjectTypeChange(type) {
        this.queryParams.projectType = type
        this.fetchProjects()
      },
      handleStatusChange(status) {
        this.selectedStatus = status
        this.queryParams.alarmLevel = status
        this.fetchProjects()
      },
      handleSearch() {
        this.fetchProjects()
      },
      handleReset() {
        this.queryParams = {
          projectType: '',
          alarmLevel: '',
          name: ''
        }
        this.selectedStatus = ''
        // 通过 ref 访问子组件并调用 selectProjectType 方法选择“全部”选项
        const projectTypeSelector = this.$refs.projectTypeSelector
        if (projectTypeSelector) {
          const fullOption = projectTypeSelector.projectTypeList[0]
          projectTypeSelector.selectProjectType(fullOption)
        }
        this.fetchProjects()
      },
      handleDataManagement(projectId) {
        // 跳转监控页面逻辑
        // this.$refs['projectMonitor'].show(item.id,item.name)
        this.$store.commit('acl/setProjectId', projectId)
        this.$baseEventBus.$emit('reload-router-view')
        this.$router.replace("/dataManagementList")
      },
      showBMap(item) {
        let position = item.position.split(',')
        let marker=
          {
            position:{lng: parseFloat(position[0]), lat: parseFloat(position[1])},
            info:item.name + "<br/>" + "项目地址：" + item.position
          }
        this.$refs.BMapShow.showBaiDuMap(marker)
      },
    }
  }
</script>

<style lang="scss" scoped>
  .index-container {
    background: #f6f8f9;
    padding: 0;
    .status-item {
      padding: 12px 0;
      text-align: center;
      border-bottom: 1px solid #ebeef5;
      cursor: pointer;
      transition: all 0.3s;
      span{
        vertical-align: super;
        background-color: rgb(245, 108, 108);
        color: rgb(255, 255, 255);
        display: inline-block;
        font-size: 12px;
        line-height: 18px;
        text-align: center;
        border-radius: 10px;
        padding: 0px 6px;
        white-space: nowrap;
        border-width: 1px;
        border-style: solid;
        border-color: rgb(255, 255, 255);
        border-image: initial;
        margin-left: -2px;
      }
      &:hover {
        background: #f5f7fa;
        color: #409EFF;
      }
      &.active {
        color: #409EFF;
        font-weight: 500;
        background: #f0f7ff;
      }
    }
    .project-card {
      margin-bottom: 20px;
      .card-content {
        display: flex;
        position: relative;
        .project-image {
          width: 280px;
          height: 200px;
          border: 1px solid #f0f0f0;
          border-radius: 4px;
          object-fit: cover;
          margin-right: 20px;
        }
        .project-info {
          flex: 1;
          position: relative;
          .project-title {
            margin: 0 0 12px;
            font-size: 18px;
            color: #000;
          }
          .project-desc {
            color: #666;
            margin: 0 0 10px 0;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
          .project-footer {
            position: absolute;
            border-top: 1px solid #f0f0f0;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 12px;
          }
        }
      }
    }
  }
</style>
