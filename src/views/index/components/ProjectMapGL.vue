<template>
  <el-card shadow="hover">
    <template #header>项目地图</template>
    <div class="mapBox" :style="{height: mapHeight + 'px'}">
      <div id="mapGLBox"></div>
    </div>
    <!-- 项目详情 -->
    <el-dialog
      top="10vh"
      title="项目详情"
      :visible.sync="dialogDetails"
      :close-on-click-modal="true"
      append-to-body
      width="700px"
      @close="closeDialog"
    >
      <div class="detail-box">
        <div class="detail-img">
          <img :src="projectInfo.pic" @click="openPreview" />
        </div>
        <el-descriptions :column="1" border size="medium">
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{projectInfo.name}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目类型</template>
            {{projectInfo.type}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">安装时间</template>
            {{projectInfo.startTime}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目地址</template>
            {{projectInfo.address}}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="link-btn">
        <el-link type="primary" @click="toOnlineMonitor">在线监控</el-link>
        <el-link type="primary">/</el-link>
        <el-link type="primary" @click="toDeviceInfo">设备信息</el-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="dialogDetails = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <ElImageViewer
      v-if="showImageViewer"
      :url-list="imgList"
      :on-close="closePreview"
      :mask-closable="false"
      append-to-body
      style="z-index: 999999999;"
    />
  </el-card>
</template>

<script>
  import { getDictList } from '@/api/system/dict-api'
  import {
    getProjectListWithAuth,
    getDataProject
  } from '@/api/project/project-api'
  import { getProjectAndAlarm } from '@/api/projectMap/index'
  export default {
    props: {
      height: {
        type: Number,
        default: 0
      }
    },
    computed: {
      mapHeight() {
        return this.height - 90
      },
    },
    components: {
      ElImageViewer: () => import('element-ui/packages/image/src/image-viewer')
    },
    data() {
      return {
        projectList: [],
        projectInfo: {},
        dialogDetails: false,
        showImageViewer: false,
        imgList: [],
        map: null,
        projectTypeList: [],
      }
    },
    async mounted() {
      await this.getAllProjectType()
      await this.getProjectList()
      this.createMap()
    },
    beforeDestroy() {
      if (this.map) {
        this.map.clearOverlays()
        this.map = null
      }
    },
    methods: {
      openPreview() {
        this.imgList = [this.projectInfo.pic]
        this.showImageViewer = true
      },
      closePreview() {
        this.showImageViewer = false
        this.imgList = []
      },
      async changeProjectByParentType(type) {
        this.map.clearOverlays()
        await this.getProjectList(type)
        let mapPoint = null
        if (this.projectList.length > 0) {
          const project = this.projectList[0]
          mapPoint = new BMapGL.Point(project.longitude, project.latitude)
        }
        const point = mapPoint || new BMapGL.Point(116.404, 39.915)
        this.map.centerAndZoom(point, 8)
        this.map.enableScrollWheelZoom(true)
        this.createrMarker()
      },
      // 创建地图
      createMap() {
        let mapPoint = null
        if (this.projectList.length > 0) {
          const project = this.projectList[0]
          mapPoint = new BMapGL.Point(project.longitude, project.latitude)
        }
        this.map = new BMapGL.Map('mapGLBox', {
          enableMapClick: false,
          minZoom: 5,
          maxZoom: 14,
          mapType:BMAP_EARTH_MAP,//BMAP_NORMAL_MAP	此地图类型展示普通街道视图   BMAP_EARTH_MAP	此地图类型展示地球卫星视图
          preserveDrawingBuffer: true
        })
        const point = mapPoint || new BMapGL.Point(116.404, 39.915)
        this.map.centerAndZoom(point, 7)
        this.map.enableScrollWheelZoom(true)
        this.map.addDistrictLayer(new BMapGL.DistrictLayer({
          name: '(江西省)',
          strokeColor: '#00f',
          strokeWeight: 4,
          strokeOpacity: 0.01,
          fillColor: 'rgba(0, 0, 0, 0)',
        }))
        this.createrMarker()
      },
      async markClick(item) {
        const {id, name, type, address, pic, startTime} = item
        this.projectInfo = {
          id,
          name,
          type,
          address,
          pic,
          startTime,
        }
        this.dialogDetails = true
      },
      createrMarker() {
        let _this = this
        const projectList = this.projectList
        if (!projectList.length) return
        projectList.forEach((item, index) => {
          let nameBg;
          if(item.alarmLevel !== undefined && item.alarmLevel !== null) {
            nameBg = require(`@/assets/project_map/level_${item.alarmLevel}.png`)
          }
          let customOverlay = 'customOverlay' + item.id
          customOverlay = new BMapGL.CustomOverlay(createDOM, {
            point: new BMapGL.Point(item.longitude, item.latitude),
            opacity: 0.5,
            offsetY: -10,
            enableMassClear: true,
            properties: {
              data: item,
              projectName: item.name,
              nameBg,
              iconBg: require('@/assets/project_map/mark.png'),
            }
          });
          this.map.addOverlay(customOverlay);
          customOverlay.addEventListener('click', function (e) {
            const data = e.target.properties.data;
             _this.markClick(data)
          });
        })
        function createDOM() {
          const div = document.createElement('div')
          div.style.zIndex = BMapGL.Overlay.getZIndex(this.point.lat);
          div.style.width = '206px'
          div.style.position = "absolute";
          div.style.overflow = 'hidden'
          // 项目名称
          const title = document.createElement('div')
          title.style.width = '100%'
          title.style.height = '43px'
          title.style.lineHeight = '43px'
          title.style.boxSizing = 'border-box'
          title.style.padding = '0 30px'
          title.style.fontSize = '14px'
          title.style.color = '#fff'
          title.style.textAlign = 'center'
          title.style.whiteSpace = 'nowrap'
          title.style.textOverflow = 'ellipsis'
          title.style.overflow = 'hidden'
          title.style.backgroundImage = `url(${this.properties.nameBg})`
          title.style.backgroundSize = '100% 100%'
          title.appendChild(document.createTextNode(this.properties.projectName));
          div.appendChild(title);
          // 标注
          const icon = document.createElement('div')
          icon.style.width = '53px'
          icon.style.height = '47px'
          icon.style.margin = '0 auto'
          icon.style.backgroundImage = `url(${this.properties.iconBg})`
          icon.style.backgroundSize = '100% 100%'
          div.appendChild(icon);
          return div
        }
      },

      async getProjectList(projectType = '') {
        const { result } = await getProjectAndAlarm({projectType})
        this.projectList = result.map(item => {
          const {
            address,
            alarmLevel,
            alarmType,
            id,
            name,
            projectType,
            pic,
            startTime,
            position
          } = item
          const lnglatData = position && position.split(',')
          return {
            address,
            alarmLevel,
            alarmType,
            id,
            name,
            type: this.getProjectTypeName(projectType),
            pic: pic ?? require('@/assets/empty_images/data_empty.png'),
            startTime,
            longitude: lnglatData[0],
            latitude: lnglatData[1]
          }
        })
      },
      async getAllProjectType() {
        const { result } = await getDictList({ dictCode: 'projectType' })
        this.projectTypeList = result[0].children.map(item => {
          return {
            label: item.dictName,
            value: item.dictCode
          }
        })
      },
      getProjectTypeName(ProjectType) {
        if (!ProjectType) return ''
        for (const data of this.projectTypeList) {
          if (data.value == ProjectType) {
            return data.label
          }
        }
      },
      closeDialog() {
        this.projectInfo = {}
      },
      toOnlineMonitor() {
        this.dialogDetails = false
        this.$store.commit('acl/setProjectId', this.projectInfo.id)
        this.$baseEventBus.$emit('reload-router-view')
        this.$router.replace("/index")
      },
      toDeviceInfo() {
        this.dialogDetails = false
        this.$store.commit('acl/setProjectId', this.projectInfo.id)
        this.$baseEventBus.$emit('reload-router-view')
        this.$router.replace("/deviceManagement")
      },
    }
  }
</script>

<style lang="scss" scoped>
  #mapGLBox {
    position: relative;
    width: 100%;
    height: 100%;
  }
  ::v-deep .detail-box {
    display: flex;
    align-items: center;
    .detail-img {
      width: 250px;
      margin-right: 20px;
      img {
        width: 100%;
      }
    }
    .el-descriptions-item__label {
      width: 100px;
    }
    .el-descriptions-item__content {
      width: 250px;
    }
  }
  .link-btn {
    margin-top: 20px;
  }
</style>
