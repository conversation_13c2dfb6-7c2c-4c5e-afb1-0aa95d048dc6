<template>
  <el-card shadow="hover">
    <template #header>项目地图
<!--      <el-button @click="openDrawPolygon">画多边形</el-button>-->
<!--      <el-button @click="openDrawCircle">画圆</el-button>-->
<!--      <el-button @click="clearDraw">清除</el-button>-->
    </template>
    <div class="mapBox" :style="{height: mapHeight + 'px'}">
      <div id="mapTBox"></div>
    </div>

    <!-- 项目详情 -->
    <el-dialog
      top="10vh"
      title="项目详情"
      :visible.sync="dialogDetails"
      :close-on-click-modal="true"
      append-to-body
      @closed="closeDialog"
    >
      <div class="detail-bpx" v-loading="dialogLoading">
        <el-descriptions class="margin-top" :column="2" border size="medium">
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{projectInfo.name}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目类型</template>
            {{projectInfo.type}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">安装时间</template>
            {{projectInfo.startTime}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目地址</template>
            {{projectInfo.address}}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="link-btn" style=" margin-top: 10px">
        <el-link type="primary" @click="toOnlineMonitor">在线监控</el-link>
        <el-link type="primary">/</el-link>
        <el-link type="primary" @click="toDeviceInfo">设备信息</el-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="dialogDetails = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
    <ElImageViewer
      v-if="showImageViewer"
      :url-list="imgList"
      :on-close="closePreview"
      :mask-closable="false"
      append-to-body
      style="z-index: 999999999;"
    />
  </el-card>
</template>

<script>
  import {
    getProjectListWithAuth,
    getDataProject
  } from '@/api/project/project-api'
  import gcoord from 'gcoord';
  import jxGeoJson from '@/assets/jx_geojson.js'
  let handler = null
  export default {
    props: {
      height: {
        type: Number,
        default: 0
      }
    },
    computed: {
      mapHeight() {
        return this.height - 110
      },
    },
    data() {
      return {
        projectList: [],
        projectInfo: {},
        dialogLoading: false,
        dialogDetails: false,
        customOverlays: [],
      }
    },
    async mounted() {
      await this.getProjectList()
      this.createMap()
    },
    beforeDestroy() {
      if (this.map) {
        this.map.clearOverLays()
        this.map = null
      }
    },
    methods: {
      // 创建地图
      createMap() {
        let mapPoint = null
        if (this.projectList.length > 0) {
          const project = this.projectList[0]
          mapPoint = new T.LngLat(project.longitude, project.latitude)
        }
        this.map = new T.Map('mapTBox', {
          projection: 'EPSG:4326',
          minZoom: 4,
          // maxZoom: 14
        })
        this.map.centerAndZoom(mapPoint || new T.LngLat(116.40093, 39.90313), 7);



        //创建对象
        var ctrl = new T.Control.MapType();
        //添加控件
        this.map.addControl(ctrl);
        this.map.setMapType(TMAP_HYBRID_MAP)

        let points = [];
        const originPoints = jxGeoJson.features[0].geometry.coordinates[0][0]
        const len = originPoints.length
        for(let i = 0; i < len; i++) {
          points.push(new T.LngLat(originPoints[i][0], originPoints[i][1]))
        }
        const polygon = new T.Polygon(points,{
          color: "blue",
          weight: 3,
          opacity: 0.5,
          // fillColor: '',
          fillOpacity: 0.1,
        });
        //向地图上添加面
        this.map.addOverLay(polygon);
        this.createrMarker()
        console.log(this.map.getOverlays())
      },
      createrMarker() {
        let _this = this
        const projectList = this.projectList
        if (!projectList.length) return
        let definedOverlay = T.Overlay.extend({
          initialize: function(lnglat, projectName, projectId, options) {
            this.lnglat = lnglat
            this._projectName = projectName
            this._projectId = projectId
            this.setOptions(options)
          },
          onAdd: function(map) {
            let div = this._div = document.createElement("div");
            div.style.position = "absolute"
            div.style.zIndex = 99999
            div.style.width = '206px'
            div.style.height = '90px'
            div.style.transform = 'translate(-103px, -45px)'
            // 项目名称
            let title = document.createElement('div')
            title.style.width = '100%'
            title.style.height = '43px'
            title.style.lineHeight = '43px'
            title.style.boxSizing = 'border-box'
            title.style.padding = '0 30px'
            title.style.fontSize = '14px'
            title.style.color = '#fff'
            title.style.cursor = 'pointer'
            title.style.textAlign = 'center'
            title.style.whiteSpace = 'nowrap'
            title.style.textOverflow = 'ellipsis'
            title.style.overflow = 'hidden'
            title.style.backgroundImage = 'url(https://minio.jxth.com.cn/files/bridgeMonitoring/ed3712f8-9e75-4ddc-ad92-378fd571de93.png)'
            title.style.backgroundSize = '100% 100%'
            div.appendChild(title);
            title.appendChild(document.createTextNode(this._projectName));
            // 标注图标
            const icon = document.createElement('div')
            icon.style.width = '53px'
            icon.style.height = '47px'
            icon.style.margin = '0 auto'
            icon.style.backgroundImage = 'url(https://minio.jxth.com.cn/files/bridgeMonitoring/a37f5fa5-e029-4667-82e3-db9a265ce7c7.png)'
            icon.style.backgroundSize = '100% 100%'
            div.appendChild(icon);
            this._div.addEventListener("click", () => {
              _this.markClick(this._projectId)
            })
            map.getPanes().overlayPane.appendChild(this._div);
            this.update(this.lnglat);
          },
          onRemove: function() {
            const parent = this._div.parentNode;
            if (parent) {
              parent.removeChild(this._div);
              this._div = null;
            }
          },
          setLnglat: function (lnglat) {
            this.lnglat = lnglat
            this.update()
          },
          getLnglat: function () {
            return this.lnglat
          },
          setPos: function (pos) {
            this.lnglat = _this.map.layerPointToLngLat(pos);
            this.update();
          },
          update: function() {
            var pos = _this.map.lngLatToLayerPoint(this.lnglat);
            this._div.style.top = (pos.y - 36) + "px";
            this._div.style.left = (pos.x - 11) + "px";
          },
        })
        projectList.forEach((item, index) => {
          let point = new T.LngLat(item.longitude, item.latitude)
          let pdefinedOverlay = new definedOverlay(point, item.name, item.id, {});
          this.map.addOverLay(pdefinedOverlay);
        })
      },
      async markClick(projectId) {
        this.dialogDetails = true
        this.dialogLoading = true
        await getDataProject({
          id: projectId
        }).then(res => {
          const projectInfo = res.result[0]
          const {
            name,
            type,
            address,
            len,
            introduce,
            pic
          } = projectInfo
          this.projectInfo = {
            name,
            type,
            address,
            len,
            introduce,
            pic
          }
          this.dialogLoading = false
        })
      },
      async getProjectList() {
        await getProjectListWithAuth({}).then((res) => {
          const projectData = res.result || []
          let data = []
          projectData.forEach(item => {
            if (item.position) {
              const lnglatData = item.position.split(',')
              const result = gcoord.transform(
                [lnglatData[0], lnglatData[1]],    // 经纬度坐标
                gcoord.BD09,               // 当前坐标系
                gcoord.WGS84                 // 目标坐标系
              );
              const dataItem = {
                id: item.id,
                name: item.name,
                longitude: result[0],
                latitude: result[1]
              }
              data.push(dataItem)
            }
          })
          this.projectList = data
        })
      },
      closeDialog() {
        this.projectInfo = {}
      },
      openDrawPolygon() {
        let _this = this
        if(handler) {
          handler.close();
        }
        handler = new T.PolygonTool(this.map, {
          color: 'blue',
          weight: 3,
          opacity: 0.5,
          fillColor: '#0000FF',
          fillOpacity: 0.2,
        });
        handler.open();
        handler.addEventListener('draw', function(e) {
          const { currentLnglats } = e
          console.log(currentLnglats)
        })
      },
      openDrawCircle() {
        let _this = this
        if (handler) {
          handler.close();
        }
        handler = new T.CircleTool(this.map, {follow: true});
        handler.open();
        handler.addEventListener('draw', function(e) {
          const { currentCenter,currentRadius } = e
          // console.log(currentCenter, currentRadius)
          console.log(_this.map.getOverlays())
        })
      },
      clearDraw() {
        if(!this.map) return
        this.map.clearOverLays()
      },
      toOnlineMonitor() {
        this.dialogDetails = false
        this.$store.commit('acl/setProjectId', this.projectInfo.id)
        this.$baseEventBus.$emit('reload-router-view')
        this.$router.replace("/index")
      },
      toDeviceInfo() {
        this.dialogDetails = false
        this.$store.commit('acl/setProjectId', this.projectInfo.id)
        this.$baseEventBus.$emit('reload-router-view')
        this.$router.replace("/deviceManagement")
      },
    }
  }
</script>

<style lang="scss" scoped>
  #mapTBox {
    position: relative;
    width: 100%;
    height: 100%;
  }
  ::v-deep .detail-bpx {
    .el-descriptions-item__label {
      width: 150px;
    }
    .el-descriptions-item__content {
      width: 350px;
    }
  }
</style>
<style>
  .tdt-control-copyright {
    display: none;
  }
</style>
