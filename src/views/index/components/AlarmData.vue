<template>
  <el-card shadow="hover">
    <template #header>预警信息</template>
    <div :style="{height: scrollbarHeight + 'px'}" v-loading="loading">
      <div class="list-wrapper" v-if="alarmList.length > 0">
        <vue-seamless-scroll
          :data="alarmList"
          :class-option="optionSetting"
          class="seamless-warp"
        >
          <div class="list-item" v-for="(item, index) in alarmList" :key="index">
            <div class=list-con>
              <div class="item">监测项目: {{ item.projectName }}</div>
              <div class="item">监测项: {{ item.deviceTypeName }}</div>
              <div class="item">设备编号: {{ item.deviceCode }}</div>
              <div class="item">预警类型: {{ item.alarmTypeName }}</div>
              <div class="item">预警数值: {{ item.alarmData }}</div>
              <div class="item">采集时间: {{ item.prodTime }}</div>
              <div class="item">处理状态:
               <el-tag v-if="item.status === '-1'" type="primary">已发送(业主)</el-tag>
               <el-tag v-if="item.status === '0'" type="danger">待处理</el-tag>
               <el-tag v-if="item.status === '1'" type="success">已处理(忽略)</el-tag>
              </div>
            </div>
          </div>
        </vue-seamless-scroll>
      </div>
      <div v-else class="no-data">
        <img :src="require('@/assets/empty_images/data_empty.png')" />
      </div>
    </div>
  </el-card>
</template>

<script>
  import { getAlarmDataByPage } from '@/api/alarming/alarmData-api.js'
  export default {
    props: {
      height: {
        type: Number,
        default: 0
      }
    },
    computed: {
      scrollbarHeight() {
        return this.height - 100
      },
      optionSetting() {
        return {
          step: 0.5,
          hoverStop: true,
          limitMoveNum: 5,
        }
      },
      projectId() {
        return this.$store.getters['acl/projectId']
      }
    },
    data() {
      return {
        loading: false,
        alarmList: []
      }
    },
    watch: {
      'projectId': {
        handler(newVal) {
          // 获取项目预警数据
          this.getAlarmData()
        },
        immediate: true
      }
    },
    async created() {
      // await this.getAlarmData()
    },
    methods: {
      async getAlarmData() {
        this.loading = true
        const params = {
          curPage: 1,
          pageSize: 10,
          deviceType: '',
         // projectId: this.projectId,
        }
        await getAlarmDataByPage(params).then(res => {
          this.loading = false
          const list = res.result.records
          this.alarmList = list
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  ::v-deep .el-card__body {
    padding: 20px 0 !important;
  }
  .list-wrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .list-item {
    width: 100%;
    box-sizing: border-box;
    padding: 5px 20px;
    margin-bottom: 10px;
    .list-con {
      width: 100%;
      height: 100%;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      box-sizing: border-box;
      padding: 10px 10px 5px;
      font-size: 12px;
      .item {
        margin-bottom: 10px;
      }
    }
  }
  .no-data {
    margin-top: 200px;
    text-align: center;
    img {
      width: 200px
    }
  }
</style>
