<template>
  <div class="index-container">
    <el-row :gutter="20">
      <el-col :lg="24" :md="24" :xl="24">
        <div class="warning-box">
            <el-skeleton :loading="loading" animated :count="4" :throttle="500">
              <template slot="template">
                <li class="loading">
                  <div class="warning-icon">
                  </div>
                  <div class="warning-data">
                    <span></span>
                    <span></span>
                  </div>
                </li>
              </template>
              <template>
                <ul>
                  <li v-for="(item, index) in warningInfo" :key="index" :style="{background: item.bgColor}">
                    <div class="warning-icon">
                      <vab-icon-mix :icon="item.warningIcon" size="45" />
                    </div>
                    <div class="warning-data">
                      <span>{{item.projectNum}}</span>
                      <span>{{item.levelName}}(处)</span>
                    </div>
                  </li>
                </ul>
              </template>
            </el-skeleton>
        </div>
      </el-col>
      <el-col :lg="24" :md="24" :xl="24">
        <el-card shadow="hover" class="elcard_d">
          <h4 style="display: inline-block">项目类型：</h4>
        <el-badge  class="item" v-for="item in projectTypeList" :value="item.count || ''"  :key="item.value">
          <el-button size="small" @click="changeProjectList(item)">{{ item.label }}</el-button>
        </el-badge>
        </el-card>
      </el-col>
      <el-col :lg="18" :md="18" :xl="18">
<!--        <ProjectMapGL ref="ProjectMapGLRef" :style="{height: cardHeight + 'px'}" :height="cardHeight" style="margin-bottom: 0;" />-->
        <ProjectMapT :style="{height: cardHeight + 'px'}" :height="cardHeight" style="margin-bottom: 0;" />
      </el-col>
      <el-col :lg="6" :md="6" :xl="6">
        <AlarmData :style="{height: cardHeight + 'px'}" :height="cardHeight" style="margin-bottom: 0;" />
      </el-col>
    </el-row>
  </div>
</template>

<script>
  import { mapGetters } from 'vuex'
  // import ProjectMapGL from './components/ProjectMapGL'
  import ProjectMapT from './components/ProjectMap_T'
  import AlarmData from './components/AlarmData'
  import screenfull from 'screenfull'
  import { getProjectCountForLevel,getProjectTypeSum } from '@/api/projectMap/index'
  const warningConfig = [
    {level: 1, levelName: '红色预警', bgColor: '#ff494f', warningIcon: 'level_1'},
    {level: 2, levelName: '橙色预警', bgColor: '#ff9000', warningIcon: 'level_2'},
    {level: 3, levelName: '黄色预警', bgColor: '#FFD700', warningIcon: 'level_3'},
    {level: 4, levelName: '蓝色预警', bgColor: '#259bff', warningIcon: 'level_4'},
  ]
  export default {
    name: 'Index',
    components: {
      AlarmData,ProjectMapT
      // ProjectMapGL
    },
    computed: {
      ...mapGetters({userId: 'user/userid'}),
      cardHeight() {
        return this.isFullscreen ? this.baseHeight + 170 : this.baseHeight
      }
    },
    data() {
      return {
        isFullscreen: false,
        baseHeight: 720,
        warningInfo: [],
        loading: true,
        projectTypeList:[],
        projectTypeValue: 'all', // 项目类型默认展示全部
      }
    },
    created() {
      this.getWarningData()
      this.getAllProjectType()
    },
    mounted() {
      this.init()
    },
    beforeDestroy() {
      this.destroy()
    },
    methods: {
      async getAllProjectType() {
        const res = await getProjectTypeSum({})
        const result = res.result
        let allCount = 0
        this.projectTypeList = result.map(item => {
          allCount += Number(item.projectCount || 0)
          return {
            label: item.typeName,
            count: Number(item.projectCount),
            value: item.projectType
          }
        })
        this.projectTypeList.unshift({
          label: '全部',
          count: allCount,
          value: 'all'
        })
      },
      async getWarningData() {
        this.loading = true
        const params = {
          userId: this.userId,
        }
        const { result } = await getProjectCountForLevel(params)
        this.warningInfo = warningConfig.map(config => {
          const idx = result.findIndex(item => item.alarmLevel === config.level)
          if(idx >= 0) {
            return {...config, ...result[idx]}
          } else {
            return {...config, projectNum: 0}
          }

        })
        this.loading = false
      },
      change() {
        this.isFullscreen = screenfull.isFullscreen
      },
      init() {
        if (screenfull.isEnabled) screenfull.on('change', this.change)
      },
      destroy() {
        if (screenfull.isEnabled) screenfull.off('change', this.change)
      },
      changeProjectList(projectItem) {
        const { value } = projectItem
        if(value === this.projectTypeValue) return
        this.projectTypeValue = value
        this.$refs.ProjectMapGLRef.changeProjectByParentType(value !== 'all' ? value : '')
      },
    }
  }
</script>
<style>
html body .elcard_d .el-card__body{
  padding: 10px;
}
</style>
<style lang="scss" scoped>
  .index-container {
    padding: 0 !important;
    background: $base-color-background !important;

    .warning-box {
      width: 100%;
      box-sizing: border-box;
      padding: 15px 20px;
      background-color: #fff;
      margin-bottom: 20px;
      ::v-deep .el-skeleton {
        display: flex;
      }
      ul {
        display: flex;
        align-items: center;
        // justify-content: space-between;
        list-style: none;
        padding: 0;
        margin: 0;
      }
      li {
        width: 20%;
        height: 70px;
        display: flex;
        align-items: center;
        border-radius: 4px;
        color: #fff;
        padding: 0;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.12);
        cursor: pointer;
        user-select: none;
        margin-right: 20px;
        &:active {
          box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
        }
        .warning-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 50px;
          height: 50px;
          margin: 0 15px;
        }
        .warning-data {
          span {
            display: block;
            font-size: 12px;
            min-width: 70px;
            &:nth-child(1) {
              font-weight: bold;
              font-size: 24px;
              margin-bottom: 10px;
            }
          }
        }
        &.loading {
          .warning-icon {
            background-color: #f1f2f3;
          }
          span {
            display: block;
            width: 100px;
            height: 10px;
            background-color: #f1f2f3;
             &:nth-child(1) {
               width: 70px;
               margin-bottom: 15px;
             }
          }
        }
      }
    }
    .item {
      margin-right: 20px;
    }

    ::v-deep {
      .access,
      .authorization,
      .version-information {
        min-height: 268px;
      }

      .el-card {
        .el-card__header {
          position: relative;

          .card-header-tag {
            position: absolute;
            top: 15px;
            right: $base-margin;
          }

          > div > span {
            display: flex;
            align-items: center;

            i {
              margin-right: 3px;
            }
          }
        }

        .el-card__body {
          position: relative;

          .echarts {
            width: 100%;
            height: 127px;
          }

          .card-footer-tag {
            position: absolute;
            right: $base-margin;
            bottom: 15px;
          }
        }
      }

      .bottom {
        padding-top: 20px;
        margin-top: 5px;
        color: #595959;
        text-align: left;
        border-top: 1px solid $base-border-color;
      }
    }
  }
</style>
