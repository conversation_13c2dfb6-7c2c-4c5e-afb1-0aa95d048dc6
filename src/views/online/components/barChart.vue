<template>
  <div :class="className" :style="{height:height,width:width}"/>
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme


export default {
    props: {
        className: {
            type: String,
            default: 'chart'
        },
        width: {
            type: String,
            default: '100%'
        },
        height: {
            type: String,
            default: '350px'
        },
        autoResize: {
            type: Boolean,
            default: true
        },
        chartData: {
            type: Object,
            required: true
        },
        yAxisName: {
            type: String,
            default: '',
            required: true
        }
    },
    data() {
        return {
            chart: null
        }
    },
    watch: {
        chartData: {
            deep: true,
            handler() {
                this.setOptions()
            }
        },
        yAxisName: {
            deep: true,
            handler() {
                this.setOptions()
            }
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.initChart()
        })
    },
    beforeDestroy() {
        if (!this.chart) {
            return
        }
        this.chart.dispose()
        this.chart = null
    },
    methods: {
        initChart() {
            this.chart = echarts.init(this.$el, 'macarons')
            this.setOptions()
        },
        setOptions() {
            // 组装series
            const series = [];
            const map = new Map(Object.entries(this.chartData.dataY));
            for (let i = 0; i < this.chartData.legend.length; i++) {
                const name = this.chartData.legend[i];
                const sere = {
                    name: name,
                    data: map.get(name),
                    type: "bar",
                    connectNulls: true,
                    smooth: true,
                    symbol: "none",
                    animationDuration: 2800,
                    animationEasing: "cubicInOut",
                };
                series.push(sere);
            }

            // 图表
            this.chart.setOption(
                {
                    legend: {
                        type: "scroll", // 分页类型
                        data: this.chartData.legend,
                        orient: "vertical", // 垂直显示
                        y: "center", // 延Y轴居中
                        x: "right", // 居右显示
                        textStyle: {
                          color: '#1890ff',
                        },
                    },
                    toolbox: {
                      show: true,
                      right: 0,
                      top: 0,
                      feature: {
                        saveAsImage: {
                          title: '',
                          name: '趋势图',
                          type: 'png'
                        }
                      }
                    },
                    xAxis: {
                        type: "category",
                        data: this.chartData.dataX
                    },
                    yAxis: {
                        type: "value",
                        name: this.yAxisName,
                        scale: true
                    },
                    series: series,
                    grid: {
                        left: 30,
                        right: "12%",
                        bottom: 10,
                        top: 30,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: "axis",
                        axisPointer: {
                            type: "cross"
                        },
                        padding: [5, 10]
                    }
                },
                true
            );

            /*this.chart.setOption({
                legend: {
                    type: 'scroll', // 分页类型
                    data: 'hhh',
                    orient: 'vertical', // 垂直显示
                    y: 'center', // 延Y轴居中
                    x: 'right' // 居右显示
                },
                xAxis: {
                    type: 'category',
                    data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                    type: 'value',
                    name: 'kk',
                    scale: true
                },
                series: {
                    data: [820, 932, 901, 934, 1290, 1330, 1320],
                    type: 'line',
                    smooth: true
                },
                grid: {
                    left: 25,
                    right: '12%',
                    bottom: 10,
                    top: 30,
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    padding: [5, 10]
                }
            })*/
        }
    }
}
</script>
