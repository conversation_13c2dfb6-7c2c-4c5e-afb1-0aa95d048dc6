<template>
  <div class="card-container">
    <vab-query-form class="page-header">
      <vab-query-form-left-panel :span="16">
        <el-form ref="form" :inline="true" label-width="100px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="测点编号">
            <el-select v-model="queryForm.deviceId" class="date-item" filterable collapse-tags placeholder="">
              <el-option v-for="item in deviceList" :key="item.name" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间选择：">
            <el-date-picker v-model="queryForm.receiveTime" type="date" placeholder="选择日期时间" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-radio-group v-model="queryForm.switchValue" @change="changSwitch">
              <el-radio label="vibrate" class="custom-radio">时域图</el-radio>
              <el-radio label="vibrate_spectrum">频谱图</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="8">
        <el-button type="warning" @click="exportDataBtn">监测数据导出</el-button>
        <el-button type="success" @click="uploadDataBtn">测点图上传</el-button>
        <el-button type="primary" plain @click="troggleShowImg">{{ imgShow ? '关闭测点图' : '查看测点图' }}</el-button>
      </vab-query-form-right-panel>
    </vab-query-form>
    <imgInfo v-show="imgShow" ref="imgInfoRef" />
    <el-row :gutter="15">
      <el-col class="trend" :xs="24" :sm="24" :md="18" :lg="18" :xl="18">
        <el-card class="trend-card" shadow="hover" v-if="isShow">
          <template #header>
            <div style="text-align: center;">
              <span>
                <vab-icon icon="line-chart-fill" />
                时域趋势图
              </span>
            </div>
          </template>
          <line-chart :chart-data='chartData' :yAxisName="alarmInfo.yaxis" class="trend-echart"
            theme="vab-echarts-theme" />
        </el-card>
        <el-card class="trend-card" shadow="hover" v-if="!isShow">
          <template #header>
            <div style="text-align: center;">
              <span>
                <vab-icon icon="line-chart-fill" />
                频谱趋势图
              </span>
            </div>
          </template>
          <vibrate-spectrum-line-chart :chart-data='chartData' :yAxisName="alarmInfo.yaxis" class="trend-echart"
            theme="vab-echarts-theme" />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
        <alarm-info :alarmInfo="alarmInfo" :unit="alarmInfo.unit" />
      </el-col>
    </el-row>
    <el-row>
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix" style="text-align: center">
          <span class="role-span">监测数据</span>
        </div>
        <div>
          <el-table ref="tableSort" v-loading="listLoading" border :data="list" stripe>
            <el-table-column v-for="(item, index) in columns" :key="index" :align="item.center ? item.center : 'center'"
              :label="item.Comment" :prop="item.Field" :sortable="item.sortable ? item.sortable : false"
              :width="item.width ? item.width : 'auto'">
              <template #default="{ row }">
                <span v-if="item.Field === 'device_id'">
                  {{ getDeviceName(row.device_id) }}
                </span>
                <span v-else>{{ row[item.Field] }}</span>
              </template>
            </el-table-column>
            <template #empty>
              <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
            </template>
          </el-table>
          <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-size="pageInfo.pageSize"
            :total="pageInfo.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </el-card>
    </el-row>
    <CommonUploadLargeFileFdfsPopup ref="CommonUploadLargeFileFdfsPopup" class="uploadSlot" />
  </div>
</template>

<script>
import tableMix from '@/views/mixins/table'
import lineChart from './lineChart'
import vibrateSpectrumLineChart from './vibrateSpectrumLineChart'
import imgInfo from './imgInfo'
import { getDataDeviceManagement } from '@/api/device/deviceManagement-api.js'
import { parseTime, formatTimeByDay } from '@/utils/index'
import { getHistoryChartData, getMaxMinAlarmData, exportData } from '@/api/online/online-api'
import {
  getDataManagementByPage, getTableHead
} from '@/api/dataManagement/dataManagement-api.js'
import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'
import alarmInfo from './alarmInfo'
export default {
  name: 'Card',
  components: { lineChart, vibrateSpectrumLineChart, imgInfo, CommonUploadLargeFileFdfsPopup, alarmInfo },
  mixins: [tableMix],
  props: {
    nowProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryForm: {
        deviceId: '',
        timeQ: '',
        receiveTime: parseTime(formatTimeByDay(new Date(), -1), "{y}-{m}-{d}"),
        startTime: '',
        endTime: '',
        switchValue: 'vibrate',
        isShow: '',
        pageNo: 1,
        pageSize: 20,
        title: '',
      },
      isShow: true,
      type: '',
      unit: '',
      projectId: '',
      deviceList: [],
      chartData: {
        dataX: [],
        dataY: {},
        legend: []
      },
      list: [],
      listLoading: false,
      columns: [
      ],
      alarmInfo: {},
      layout: 'total, sizes, prev, pager, next, jumper',
      total: 0,
      imgShow: false,
      exportDataList: []
    }
  },
  computed: {
  },
  async created() {
    // nowProjectId是从大屏（边坡K14、赣东大桥）传递过来的数据
    this.projectId = this.nowProjectId != '' ? this.nowProjectId : sessionStorage.getItem('def_project_Id')
    this.type = this.$route.query.type
    await this.getDeviceList()
    // this.fetchData()
  },
  methods: {
    /**
     * 跳转到原始数据页面
     */
    dataPage() {
      this.$router.replace({
        path: '/originalOnline?type=' + this.type
      });
    },
    /**
     * 搜索条件下拉获取设备位置信息
     * @returns {Promise<void>}
     */
    async getDeviceList() {
      getDataDeviceManagement({ projectId: this.projectId, type: this.type })
        .then(async (res) => {
          this.deviceList = res.result
          this.queryForm.deviceId = res.result[0].id
          this.unit = res.result[0].unit
          await this.fetchData()
        })
        .catch(() => {
        })
    },
    /**
     * 获取图表数据
     */
    async getHistoryChartData() {
      this.timeQuery(this.queryForm.receiveTime)
      await getHistoryChartData({
        projectId: this.projectId, type: this.type, deviceId: this.queryForm.deviceId,
        startTime: this.queryForm.startTime, endTime: this.queryForm.endTime
      })
        .then((res) => {
          this.chartData = res.result
        })
        .catch(() => {
        })
    },
    /**
     * 获取最大最小预警数据
     */
    async getMaxMinAlarmData() {
      this.timeQuery(this.queryForm.receiveTime)
      await getMaxMinAlarmData({
        projectId: this.projectId, type: this.type, deviceId: this.queryForm.deviceId,
        startTime: this.queryForm.startTime, endTime: this.queryForm.endTime
      })
        .then((res) => {
          this.alarmInfo = res.result
        })
        .catch(() => {
        })
    },
    async fetchData() {
      this.getHistoryChartData()
      this.getMaxMinAlarmData()
      this.queryForm.curPage = this.pageInfo.curPage
      this.queryForm.pageSize = this.pageInfo.pageSize
      this.queryForm.projectId = this.projectId
      this.listLoading = true
      this.queryForm.type = this.type
      const columns = []
      const columnsList = await getTableHead(this.queryForm)
      columnsList.result.forEach(c => {
        if (c.Field === 'prod_time' || c.Field === 'device_id'
          || (c.Field.includes("val") && c.Field.startsWith("expect"))) {
          columns.push(c)
        }
        if (this.queryForm.type === 'vibrate_spectrum' && c.Field.includes("val")) {
          columns.push(c)
        }
      })
      const { result: { records, total } } = await getDataManagementByPage(this.queryForm)
      this.list = records
      this.columns = columns
      this.pageInfo.total = Number(total)
      this.listLoading = false
    },
    getDeviceName(deviceId) {
      let deviceName = ''
      this.deviceList.forEach(d => {
        if (d.id === deviceId) {
          deviceName = d.name
        }
      })
      return deviceName;
    },
    /**
     * 判断是否显示时间筛选框
     */
    openTime() {
      if (this.queryForm.timeQ === 'openTime') {
        this.queryForm.isShow = true
      } else {
        this.queryForm.isShow = false
      }
    },
    timeQuery(receiveTime) {
      if (receiveTime !== null) {
        this.queryForm.startTime = parseTime(receiveTime, "{y}-{m}-{d}") + " " + "00:00:00";
        this.queryForm.endTime = parseTime(receiveTime, "{y}-{m}-{d}") + " " + "23:59:59";
      }
    },
    uploadDataBtn() {
      const queryData = {
        bizId: this.projectId,
        bizCode: this.type + 'pointer',
        limit: 1,
        uploadType: 'img',
        // target: '/base/uploadFile/overlayUpload'
      }
      this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog(queryData)
    },
    exportDataBtn() {
      this.timeQuery(this.queryForm.receiveTime)
      this.queryForm.projectId = this.projectId
      this.queryForm.type = this.type
      exportData(this.queryForm).then(response => {
        this.downloadFile(response, "监测数据", "xlsx");
      })
    },
    troggleShowImg() {
      this.imgShow = !this.imgShow,
        this.imgShow && this.$nextTick(() => {
          this.$refs.imgInfoRef.getImgData({
            bizId: this.projectId,
            bizCode: this.type + 'pointer'
          })
        })
    },
    changSwitch() {
      this.type = this.queryForm.switchValue
      if (this.type === 'vibrate') {
        this.isShow = true
      } else {
        this.isShow = false
      }
      this.fetchData()
    }
  },
}
</script>

<style lang="scss" scoped>

  .custom-radio {
    margin-left: 20px; /* 调整左边距 */
  }
.card-container {
  padding: 0 0 $base-padding 0 !important;
  background: $base-color-background !important;

  .page-header {
    display: flex;
    align-items: center;
    padding: $base-padding $base-padding 0 $base-padding;
    margin-bottom: $base-margin;
    background: $base-color-white;
    border: 1px solid #ebeef5;

    ::v-deep {
      .el-form-item__content {
        // width: 221px !important;

        .el-select,
        .el-input,
        .el-date-editor,
        .el-checkbox-group {
          width: 100%;
        }
      }
    }
  }

  ::v-deep {
    .el-card {
      &__body {
        position: relative;
        padding: $base-padding;
        cursor: pointer;

        img {
          height: 228px;
        }

        .card-title {
          margin: $base-margin $base-margin 10px $base-margin;
          font-size: 16px;
          font-weight: bold;
        }

        .card-description {
          margin: 0 $base-margin 10px $base-margin;
        }

        .card-datetime {
          margin: 0 $base-margin 10px $base-margin;
          color: rgba($base-color-black, 0.6);
        }
      }
    }

    .el-pagination.is-background {

      .btn-next,
      .btn-prev {
        background-color: $base-color-white;
      }

      .el-pager {
        li {
          background-color: $base-color-white;

          &.active {
            background-color: $base-color-blue;
          }
        }
      }
    }
  }
}
</style>
