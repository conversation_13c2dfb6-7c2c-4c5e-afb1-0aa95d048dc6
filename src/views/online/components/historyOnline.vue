<template>
  <div class="card-container">
    <vab-query-form class="page-header">
      <vab-query-form-left-panel :span="16">
        <el-form ref="form" :inline="true" label-width="100px" :model="queryForm" @submit.native.prevent>
          <el-form-item label="测点编号">
            <el-select v-model="queryForm.deviceIds" class="date-item" style="width: 200px" collapse-tags filterable multiple placeholder="" @change="changeSelect">
              <el-checkbox
                v-model="checked"
                class="selectAllCheckBoxs"
                @change="selectAll"
                >全选</el-checkbox
              >
              <el-option v-for="item in deviceList" :key="item.name" :label="item.name" :value="item.id" />

            </el-select>

          </el-form-item>
          <el-form-item label="时间选择：">
            <el-select v-model="queryForm.timeQ" class="data-item" @change="openTime">
              <el-option label="当天" value="today" />
              <el-option label="最近3天" value="yesterday" />
              <el-option label="最近7天" value="month" />
              <el-option label="最近1个月" value="quarter" />
              <el-option label="其他" value="openTime" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="queryForm.isShow">
            <el-date-picker v-model="queryForm.receiveTime" class="date-item" :default-time="['00:00:00', '23:59:59']"
              end-placeholder="结束日期" range-separator=":" size="small" start-placeholder="开始日期" type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss" />
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" native-type="submit" type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button icon="el-icon-search" native-type="submit" type="primary"
                       v-permissions="{permission:['online:original']}"
            @click="dataPage" v-if="this.type !== 'cod' && this.type !== 'level'">
              原始数据
            </el-button>
          </el-form-item>
        </el-form>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="8">
        <el-button type="warning" @click="exportDataBtn">
          监测数据导出
        </el-button>
        <el-button type="success" @click="uploadDataBtn">测点图上传</el-button>
        <el-button plain type="primary" @click="troggleShowImg">
          {{ imgShow ? '关闭测点图' : '查看测点图' }}
        </el-button>
      </vab-query-form-right-panel>
    </vab-query-form>
    <imgInfo v-show="imgShow" ref="imgInfoRef" />
    <el-row :gutter="15">
      <el-col class="trend" :lg="18" :md="18" :sm="24" :xl="18" :xs="24">
        <el-card class="trend-card" shadow="hover">
          <template #header>
            <div style="text-align: center;">
              <span>
                <vab-icon icon="line-chart-fill" />
                {{ alarmInfo.title }}
              </span>
            </div>
          </template>
          <line-chart :chart-data="chartData" class="trend-echart" theme="vab-echarts-theme"
            :yAxisName="alarmInfo.yaxis" :alarm-info="alarmInfo" />
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="6" :lg="6" :xl="6">
        <alarm-info :alarm-info="alarmInfo" :unit="alarmInfo.unit" />
      </el-col>
    </el-row>
    <el-row>
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix" style="text-align: center">
          <span class="role-span">监测数据</span>
        </div>
        <div>
          <el-table ref="tableSort" v-loading="listLoading" border :data="list" stripe>
            <el-table-column v-for="(item, index) in columns" :key="index" :align="item.center ? item.center : 'center'"
              :label="item.Comment" :prop="item.Field" :sortable="item.sortable ? item.sortable : false"
              :width="item.width ? item.width : 'auto'">
              <template #default="{ row }">
                <span v-if="item.Field === 'device_id'">
                  {{ getDeviceName(row.device_id) }}
                </span>
                <span v-else-if="item.Field === 'expect_val'">
                  {{ getTypeNumFilter(row.type, row.expect_val) }}
                </span>
                <span v-else>{{ row[item.Field] }}</span>
              </template>
            </el-table-column>
            <template #empty>
              <el-image class="vab-data-empty" :src="require('@/assets/empty_images/data_empty.png')" />
            </template>
          </el-table>
          <el-pagination background :current-page="pageInfo.curPage" :layout="layout" :page-size="pageInfo.pageSize"
            :total="pageInfo.total" @current-change="handleCurrentChange" @size-change="handleSizeChange" />
        </div>
      </el-card>
    </el-row>
    <CommonUploadLargeFileFdfsPopup ref="CommonUploadLargeFileFdfsPopup" class="uploadSlot" />
  </div>
</template>

<script>
import tableMix from '@/views/mixins/table'
import lineChart from './lineChart'
import imgInfo from './imgInfo'
import { getDataDeviceManagement } from '@/api/device/deviceManagement-api.js'
import { parseTime, formatTimeByDay } from '@/utils/index'
import {
  getHistoryChartData,
  getMaxMinAlarmData,
  exportData,
  getTypeNumFilter
} from '@/api/online/online-api'
import {
  getDataManagementByPage,
  getTableHead,
} from '@/api/dataManagement/dataManagement-api.js'
import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'
import alarmInfo from './alarmInfo'

export default {
  name: 'Card',
  components: {
    lineChart,
    imgInfo,
    CommonUploadLargeFileFdfsPopup,
    alarmInfo,
  },
  mixins: [tableMix],
  props: {
    code: {
      type: String,
      default: ''
    },
    nowProjectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryForm: {
        deviceIds: '',
        timeQ: '',
        receiveTime: '',
        startTime: '',
        endTime: '',
        isShow: '',
        pageNo: 1,
        pageSize: 20,
        title: '',
      },
      checked: false,
      type: '',
      projectId: '',
      deviceList: [],
      chartData: {
        dataX: [],
        dataY: {},
        legend: [],
      },
      list: [],
      listLoading: false,
      columns: [],
      alarmInfo: {},
      layout: 'total, sizes, prev, pager, next, jumper',
      total: 0,
      imgShow: false,
      exportDataList: [],
    }
  },
  computed: {

  },
  async created() {
    // nowProjectId是从大屏（边坡K14、赣东大桥）传递过来的数据
    this.projectId = this.nowProjectId != '' ? this.nowProjectId : sessionStorage.getItem('def_project_Id')
    this.type = this.$route.query.type
    this.queryForm.timeQ = 'yesterday'
    await this.getDeviceList()
    // this.fetchData()
  },
  methods: {
    getTypeNumFilter,
    /**
     * 跳转到原始数据页面
     */
    dataPage() {
      this.$router.replace({
        path: '/originalOnline?type=' + this.type,
      })
    },

    // 下拉框全选
    selectAll() {
      this.queryForm.deviceIds = [];
      if (this.checked) {
        this.deviceList.map(item => {
          this.queryForm.deviceIds.push(item.id);
        });
      } else {
         this.queryForm.deviceIds = [];
      }
    },
    changeSelect(val) {
      if (val.length === this.deviceList.length) {
        this.checked = true;
      } else {
        this.checked = false;
      }
    },
    /**
     * 搜索条件下拉获取设备位置信息
     * @returns {Promise<void>}
     */
    async getDeviceList() {
      getDataDeviceManagement({ projectId: this.projectId, type: this.type })
        .then(async (res) => {
          this.deviceList = res.result
          if(this.code != '') {
            const currentDevice = this.deviceList.filter(item => item.code === this.code)
            this.queryForm.deviceIds[0] = currentDevice[0].id
          } else {
            this.queryForm.deviceIds[0] = res.result[0].id
          }
          await this.fetchData()
        })
        .catch(() => { })
    },
    /**
     * 获取图表数据
     */
    async getHistoryChartData() {
      this.timeQuery(this.queryForm.timeQ, this.queryForm.receiveTime)
      await getHistoryChartData({
        projectId: this.projectId,
        type: this.type,
        deviceIds: this.queryForm.deviceIds,
        startTime: this.queryForm.startTime,
        endTime: this.queryForm.endTime,
      })
        .then((res) => {
          this.chartData = res.result
        })
        .catch(() => { })
    },
    /**
     * 获取最大最小预警数据
     */
    async getMaxMinAlarmData() {
      this.timeQuery(this.queryForm.timeQ, this.queryForm.receiveTime)
      await getMaxMinAlarmData({
        projectId: this.projectId,
        type: this.type,
        deviceIds: this.queryForm.deviceIds,
        startTime: this.queryForm.startTime,
        endTime: this.queryForm.endTime,
      })
        .then((res) => {
          this.alarmInfo = res.result
          if (res.result.yaxis) {
            let str = res.result.yaxis.split(/,|，|\s+/)
            this.alarmInfo.yaxis = str[0]
          }
        })
        .catch(() => { })
    },
    async fetchData() {
      this.getMaxMinAlarmData()
      this.getHistoryChartData()
      this.queryForm.curPage = this.pageInfo.curPage
      this.queryForm.pageSize = this.pageInfo.pageSize
      this.queryForm.projectId = this.projectId
      this.listLoading = true
      this.queryForm.type = this.type
      const columns = []
      const columnsList = await getTableHead(this.queryForm)
      columnsList.result.forEach((c) => {
        if (
          c.Field === 'prod_time' ||
          c.Field === 'device_id' ||
          (c.Field.includes('val') && c.Field.startsWith('expect'))
        ) {
          columns.push(c)
        }
      })
      const {
        result: { records, total },
      } = await getDataManagementByPage(this.queryForm)
      this.list = records
      this.columns = columns
      this.pageInfo.total = Number(total)
      this.listLoading = false
    },
    getDeviceName(deviceId) {
      let deviceName = ''
      this.deviceList.forEach((d) => {
        if (d.id === deviceId) {
          deviceName = d.name
        }
      })
      return deviceName
    },
    /**
     * 判断是否显示时间筛选框
     */
    openTime() {
      if (this.queryForm.timeQ === 'openTime') {
        this.queryForm.isShow = true
      } else {
        this.queryForm.isShow = false
      }
    },
    timeQuery(timeQ, receiveTime) {
      let date = new Date()
      date = parseTime(date, '{y}-{m}-{d}')
      if (timeQ === 'today') {
        this.queryForm.startTime = date + ' 00:00:00'
        this.queryForm.endTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
      } else if (timeQ === 'yesterday') {
        const yesterday = formatTimeByDay(date, -3)
        this.queryForm.startTime = yesterday + ' 00:00:00'
        this.queryForm.endTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
      } else if (timeQ === 'month') {
        const month = formatTimeByDay(date, -7)
        this.queryForm.startTime = month + ' 00:00:00'
        this.queryForm.endTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
      } else if (timeQ === 'quarter') {
        const quarter = formatTimeByDay(date, -30)
        this.queryForm.startTime = quarter + ' 00:00:00'
        this.queryForm.endTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
      } else if (timeQ === 'openTime') {
        if (receiveTime !== null && receiveTime.length === 2) {
          this.queryForm.startTime = receiveTime[0]
          this.queryForm.endTime = receiveTime[1]
        }
      }
    },
    uploadDataBtn() {
      const queryData = {
        bizId: this.projectId,
        bizCode: this.type + 'pointer',
        limit: 1,
        uploadType: 'img'
        // target: '/base/uploadFile/overlayUpload',
      }
      this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog(
        queryData
      )
    },
    exportDataBtn() {
      this.timeQuery(this.queryForm.timeQ, this.queryForm.receiveTime)
      this.queryForm.projectId = this.projectId
      this.queryForm.type = this.type
      exportData(this.queryForm).then((response) => {
        this.downloadFile(response, '监测数据', 'xlsx')
      })
    },
    troggleShowImg() {
      this.imgShow = !this.imgShow,
      this.$nextTick(() => {
        this.$refs.imgInfoRef.getImgData({
          bizId: this.projectId,
          bizCode: this.type + 'pointer'
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.card-container {
  padding: 0 0 $base-padding 0 !important;
  background: $base-color-background !important;

  .page-header {
    display: flex;
    align-items: center;
    padding: $base-padding $base-padding 0 $base-padding;
    margin-bottom: $base-margin;
    background: $base-color-white;
    border: 1px solid #ebeef5;

    ::v-deep {
      .el-form-item__content {
        // width: 221px !important;

        .el-select,
        .el-input,
        .el-date-editor,
        .el-checkbox-group {
          width: 100%;
        }
      }
    }
  }

  ::v-deep {
    .el-card {
      &__body {
        position: relative;
        padding: $base-padding;
        cursor: pointer;

        img {
          height: 228px;
        }

        .card-title {
          margin: $base-margin $base-margin 10px $base-margin;
          font-size: 16px;
          font-weight: bold;
        }


        .card-description {
          margin: 0 $base-margin 10px $base-margin;
        }

        .card-datetime {
          margin: 0 $base-margin 10px $base-margin;
          color: rgba($base-color-black, 0.6);
        }
      }
    }

    .el-pagination.is-background {

      .btn-next,
      .btn-prev {
        background-color: $base-color-white;
      }

      .el-pager {
        li {
          background-color: $base-color-white;

          &.active {
            background-color: $base-color-blue;
          }
        }
      }
    }
  }
}

.selectAllCheckBoxs {
  text-align: right;
  width: 100%;
  padding-right: 10px;
}
</style>
