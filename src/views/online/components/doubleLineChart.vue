<template>
  <div :class="className" :style="{ height: height, width: width }" />
</template>

<script>
  import * as echarts from 'echarts'
  require('echarts/theme/macarons') // echarts theme

  export default {
    props: {
      className: {
        type: String,
        default: 'chart',
      },
      width: {
        type: String,
        default: '100%',
      },
      height: {
        type: String,
        default: '430px',
      },
      autoResize: {
        type: Boolean,
        default: true,
      },
      chartData: {
        type: Object,
        required: true,
      },
      unit1: {
        type: String,
        required: true,
      },
      unit2: {
        type: String,
        required: true,
      },
    },
    data() {
      return {
        chart: null,
      }
    },
    watch: {
      chartData: {
        deep: true,
        handler() {
          this.setOptions()
        },
      },
    },
    mounted() {
      this.$nextTick(() => {
        this.initChart()
        window.addEventListener('resize', () => {
          this.chart.resize()
        })
      })
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    methods: {
      initChart() {
        this.chart = echarts.init(this.$el, 'macarons')
        this.setOptions()
      },
      setOptions() {
        // 组装series
        const series = []
        const map = new Map(Object.entries(this.chartData.dataY))
        for (let i = 0; i < this.chartData.legend.length; i++) {
          const name = this.chartData.legend[i]
          let yIndex = 0
          if (name.includes('-湿度') || name.includes('-大气压力')) {
            yIndex = 1
          }
          const sere = {
            name: name,
            data: map.get(name),
            type: 'line',
            connectNulls: true,
            smooth: true,
            symbol: 'none',
            yAxisIndex: yIndex,
            animationDuration: 2800,
            animationEasing: 'cubicInOut',
          }
          series.push(sere)
        }
        // 图表
        this.chart.setOption(
          {
            legend: {
              data: this.chartData.legend,
              orient: 'vertical', // 垂直显示
              y: 'center', // 延Y轴居中
              x: 'right', // 居右显示
            },
            // dataZoom: {
            //   type: 'slider',
            //   start: 0,
            //   end: 100
            // },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              axisLine: { onZero: false },
              data: this.chartData.dataX,
            },
            yAxis: [
              {
                type: 'value',
                name: this.unit1,
              },
              {
                type: 'value',
                name: this.unit2,
              },
            ],
            series: series,
            grid: {
              left: 30,
              right: '12%',
              bottom: 60,
              top: 30,
              containLabel: true,
            },
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'cross',
              },
              padding: [5, 10],
            },
          },
          true
        )
      },
    },
  }
</script>
