<template>
  <div :class="className" :style="{height:height}" />
</template>

<script>
import * as echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme


export default {
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    height: {
      type: String,
      default: '350px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartData: {
      type: Object,
      required: true
    },
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(newVal) {
        if(JSON.stringify(newVal) !== '{}')
          this.setOptions()
      }
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      if(this.chart && JSON.stringify(this.chartData) !== '{}') {
        this.setOptions()
      }
    },
    setOptions() {
      const radiusAxisVal = this.chartData.deep
      const linepoLarData = [[0, 0], [radiusAxisVal, radiusAxisVal]]
      let {expectXval, expectYval} = this.chartData
      const scatterData = []
      this.chartData.scatterData.forEach(d =>{
        let expectXval = d.expect_xval
        let expectYval = d.expect_yval
        if(expectXval < 0) {
          expectYval = -expectYval
        }
        scatterData.push([expectXval, expectYval])
      })
      /*let {expectXval, expectYval} = this.chartData
      if(expectXval < 0) {
        expectYval = -expectYval
      }
      const scatterData = [[expectXval, expectYval]]
      const scatterData = [[expectXval, expectYval]]*/
      console.info(scatterData)
      this.chart.setOption({
        polar: {
          center: ['50%', '55%'],
          radius: '80%'
        },
        angleAxis: {
          type: 'value',
          min: 0,
          max: 360,
          splitLine: {
            show: true
          }
        },
        radiusAxis: {
          type: 'value',
          min: 0,
          max: radiusAxisVal
        },
        legend: {
          data: ['位移方位角', '坡体方向角'],      //图例名称
          //right: 0,                              //调整图例位置
          //top: 0,                                  //调整图例位置
          //itemHeight: 7,                      //修改icon图形大小
          //icon: 'circle',                         //图例前面的图标形状
          icon: 'none',                         //图例前面的图标形状
          textStyle: {                            //图例文字的样式
            color: '#a1a1a1',               //图例文字颜色
            fontSize: 12                      //图例文字大小
          },
          formatter: function (name) {
            // 根据系列名称动态设置图标
            if (name === '位移方位角') {
              return '● ' + name; // 圆点图标
            } else if (name === '坡体方向角') {
              return '→ ' + name; // 箭头图标
            }
            return name;
          },
        },
        series: [
          {
            name: '位移方位角',
            type:'scatter',
            coordinateSystem: 'polar',
            data: scatterData,
            //symbolSize: expectXval && expectYval ? 15 : 0,
            symbolSize: 3,
            itemStyle: {
              color:'red'
            },
          },
          {
            name: '坡体方向角',
            type: 'lines',
            coordinateSystem: 'polar',
            showSymbol: true,
            symbol: ['none', 'arrow'],
            symbolSize: radiusAxisVal > 0 ? 10 : 0,
            lineStyle: {
              opacity: 0.9,
            },
            data: [
              {
                coords: linepoLarData,
                lineStyle: { color: "blue", width: 2 },
              },
            ],
          }],
      })
    }
  }
}
</script>

