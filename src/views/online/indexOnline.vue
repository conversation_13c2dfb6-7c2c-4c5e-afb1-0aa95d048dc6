<template>
  <div class="index-container">
    <el-row v-loading="listloading" class="device-type">
      <div
        v-if="!deviceTypes.length"
        style="text-align: center; padding-bottom: 20px"
      >
        <el-image
          :src="require('@/assets/empty_images/data_empty.png')"
          style="width: 120px"
        />
      </div>
      <el-col
        v-for="(type, i) in deviceTypes"
        :key="i"
        class="card-panel-col"
        :lg="6"
        :md="9"
        :sm="9"
        :xl="6"
        :xs="12"
      >
        <div class="card-panel" @click="handleData(type.type)">
          <div class="card-panel-icon-wrapper icon-people">
            <img
              v-if="'humid' === type.type"
              src="@/assets/monitor/humid.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'humid_s' === type.type"
              src="@/assets/monitor/humid_s.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'water' === type.type"
              src="@/assets/monitor/water.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'rainfall' === type.type"
              src="@/assets/monitor/rainfall.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'humid_w' === type.type"
              src="@/assets/monitor/humid_w.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'strain' === type.type"
              src="@/assets/monitor/strain.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'floor' === type.type"
              src="@/assets/monitor/floor.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'displacement' === type.type"
              src="@/assets/monitor/displacement.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'optical_strain' === type.type"
              src="@/assets/monitor/strain.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'rifts' === type.type"
              src="@/assets/monitor/rifts.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'soly' === type.type"
              src="@/assets/monitor/soly.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'incline' === type.type"
              src="@/assets/monitor/incline.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'gnss' === type.type"
              src="@/assets/monitor/gnss.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'vibrate' === type.type"
              src="@/assets/monitor/vibrate.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'wind' === type.type"
              src="@/assets/monitor/wind.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'floord' === type.type"
              src="@/assets/monitor/floor.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'brilevel' === type.type"
              src="@/assets/monitor/brilevel.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'survey' === type.type"
              src="@/assets/monitor/survey.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'strain_d' === type.type"
              src="@/assets/monitor/strain.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'zl_displacement' === type.type"
              src="@/assets/monitor/displacement.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'earthPress' === type.type"
              src="@/assets/monitor/earthPress.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'bp_displacement' === type.type"
              src="@/assets/monitor/bp_displacement.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'soil_humid' === type.type"
              src="@/assets/monitor/soil_humid.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'incline_qd' === type.type"
              src="@/assets/monitor/qd.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'soly_zl' === type.type"
              src="@/assets/monitor/zl.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'floor_lz' === type.type"
              src="@/assets/monitor/lz.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'floor_dm' === type.type"
              src="@/assets/monitor/dm.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'capacity' === type.type"
              src="@/assets/monitor/battery.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'humid_dc' === type.type"
              src="@/assets/monitor/humid_dc.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'voltage' === type.type"
              src="@/assets/monitor/voltage.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'resistance' === type.type"
              src="@/assets/monitor/resistance.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'pressure' === type.type"
              src="@/assets/monitor/water.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'adcm' === type.type"
              src="@/assets/monitor/adcm.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'cod' === type.type"
              src="@/assets/monitor/cod.png"
              style="width: 60px; height: 60px"
            />
            <img
              v-if="'level' === type.type"
              src="@/assets/monitor/level.png"
              style="width: 60px; height: 60px"
            />
            <img
            v-if="'m_soly' === type.type"
            src="@/assets/monitor/m_soly.png"
            style="width: 60px; height: 60px"
            />
            <img
              v-if="'video' === type.type"
              src="@/assets/monitor/video.png"
              style="width: 60px; height: 60px"
            />
          </div>
          <div class="card-panel-description">
            <div class="card-panel-text">{{ getTypeName(type.type) }}监测</div>
            <div class="card-panel-text" style="text-align: center">
              <span>{{ type.typeSum }}</span>
              个
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row class="panel-group">
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <span class="role-span">报表下载</span>
        </div>
        <report type="projectDayReport" />
        <!-- <el-tabs type="border-card">
          <el-tab-pane label="日报">
            <report type="projectDayReport" />
          </el-tab-pane>
          <el-tab-pane label="周报">
            <report type="projectWeekReport" />
          </el-tab-pane>
          <el-tab-pane label="月报">
            <report type="projectMonthReport" />
          </el-tab-pane>
          <el-tab-pane label="季报">
            <report type="projectQuarterReport" />
          </el-tab-pane>
          <el-tab-pane label="年报">
            <report type="projectYearReport" />
          </el-tab-pane>
          <el-tab-pane label="事件响应">
            <report type="projectEventReport" />
          </el-tab-pane>
        </el-tabs> -->
      </el-card>
    </el-row>
    <!-- <el-row class="panel-group">
      <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
          <span class="role-span">数据补录</span>
          <additionalDataEntry />
        </div>
      </el-card>
    </el-row> -->
  </div>
</template>

<script>
  import report from './components/report'
  import { getDeviceTypeByProjectId } from '@/api/online/online-api'
  import { getDictList } from '@/api/system/dict-api'

  export default {
    name: 'IndexOnline',
    components: { report },
    data() {
      return {
        msg: 'vue模板页',
        deviceTypes: [],
        dictDeviceType: [],
        listloading: false,
      }
    },
    created() {
      this.getDeviceType()
      this.getDictDetails()
    },
    methods: {
      // 获取桥对应的所有设备类型
      getDeviceType() {
        this.listloading = true
        const projectId = sessionStorage.getItem('def_project_Id')
        getDeviceTypeByProjectId({ projectId: projectId })
          .then((res) => {
            this.listloading = false
            this.deviceTypes = res.result
          })
          .catch(() => {})
      },
      handleData(type) {
        if (type === 'vibrate') {
          this.$router.replace({
            path: '/vibrateOnline?type=' + type,
          })
        } else if (type === 'incline') {
          this.$router.replace({
            path: '/inclineOnline?type=' + type,
          })
        } else if (type === 'gnss') {
          this.$router.replace({
            path: '/gnssOnline?type=' + type,
          })
        } else if (type === 'wind') {
          this.$router.replace({
            path: '/windOnline?type=' + type,
          })
        } else if (type === 'humid') {
          this.$router.replace({
            path: '/humidOnline?type=' + type,
          })
        } else if (type === 'soil_humid') {
          this.$router.replace({
            path: '/soilHumidOnline?type=' + type,
          })
        } else if (type === 'survey') {
          this.$router.replace({
            path: '/surveyOnline?type=' + type,
          })
        } else if (type === 'rainfall') {
          this.$router.replace({
            path: '/rainfallOnline?type=' + type,
          })
        } else if (type === 'adcm') {
          this.$router.replace({
            path: '/adcmOnline?type=' + type,
          })
        } else if (type === 'video') {
          this.$router.replace({
            path: '/scene/videoCommon',
          })
        } else {
          this.$router.replace({
            path: '/historyOnline?type=' + type,
          })
        }
      },
      // 从数据字典获取数据
      async getDictDetails() {
        let param = { dictCode: 'deviceType' }
        const deviceTypeRes = await getDictList(param)
        const deviceTypeResult = deviceTypeRes.result[0].children
        for (const data of deviceTypeResult) {
          const treeNode = {}
          treeNode.id = data.dictCode
          treeNode.label = data.dictName
          treeNode.disabled = false
          this.dictDeviceType.push({ ...data, ...treeNode })
        }
      },
      getTypeName(type) {
        let typeName = ''
        this.dictDeviceType.forEach((data) => {
          if (data.dictCode === type) {
            typeName = data.dictName
          }
        })
        return typeName
      },
    },
  }
</script>

<style lang="scss" scoped>
  .index-container {
    background: #f6f8f9 !important;
    padding: 0 !important;

    .device-type {
      background-color: #fff;
      width: 100%;
      margin-bottom: 20px;
      padding-top: 30px;

      .card-panel-col {
        margin-bottom: 30px;
      }

      .icon-people {
        text-align: center;
        font-size: 0;
        margin-bottom: 10px;

        img {
          cursor: pointer;
        }
      }

      .card-panel-description {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          color: #f40;
          padding: 0 5px;
        }
      }
    }
  }
</style>
