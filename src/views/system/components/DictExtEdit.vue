<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1200px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="字典名称" prop="dictName">
        <el-input v-model="formData.dictName" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段1" prop="dictCode">
        <el-input v-model="formData.value1" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段2" prop="dictFullName">
        <el-input v-model="formData.value2" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段3" prop="dictFullName">
        <el-input v-model="formData.value3" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段4" prop="dictFullName">
        <el-input v-model="formData.value4" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段5" prop="dictFullName">
        <el-input v-model="formData.value5" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段6" prop="dictFullName">
        <el-input v-model="formData.value6" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段7" prop="dictFullName">
        <el-input v-model="formData.value7" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段8" prop="dictFullName">
        <el-input v-model="formData.value8" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段9" prop="dictFullName">
        <el-input v-model="formData.value9" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段10" prop="dictFullName">
        <el-input v-model="formData.value10" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段11" prop="dictFullName">
        <el-input v-model="formData.value11" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段12" prop="dictFullName">
        <el-input v-model="formData.value12" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段13" prop="dictFullName">
        <el-input v-model="formData.value13" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段14" prop="dictFullName">
        <el-input v-model="formData.value14" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段15" prop="dictFullName">
        <el-input v-model="formData.value15" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段16" prop="dictFullName">
        <el-input v-model="formData.value16" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段17" prop="dictFullName">
        <el-input v-model="formData.value17" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段18" prop="dictFullName">
        <el-input v-model="formData.value18" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段19" prop="dictFullName">
        <el-input v-model="formData.value19" style="width: 200px" />
      </el-form-item>
      <el-form-item label="扩展字段20" prop="dictFullName">
        <el-input v-model="formData.value20" style="width: 200px" />
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input
          v-model="formData.remark"
          style="width: 530px"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { getDictExtList, saveDictExt } from '@/api/system/dictExt-api'
  import { genUUID } from '@/utils/th_utils.js'

  export default {
    name: 'TableExtEdit',
    components: {},
    props: {},
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        treeDict: [],
        filterText: '',
        visible: false,
        rules: {},
      }
    },
    watch: {
      filterText(val) {
        this.$refs.dictTree.filter(val)
      },
    },
    created() {},
    methods: {
      showEdit(row, data) {
        this.treeDict = data.treeDict
        if (!row) {
          this.title = '新增字典扩展'
          this.initForm(row)
        } else {
          this.title = '编辑字典扩展'
          this.loadDataById(row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm(row) {
        this.formData = {
          id: genUUID(),
          dictId: '',
          value1: '',
          value2: '',
          value3: '',
          value4: '',
          value5: '',
          value6: '',
          value7: '',
          value8: '',
          value9: '',
          value10: '',
          value11: '',
          value13: '',
          value14: '',
          value15: '',
          value16: '',
          value17: '',
          value18: '',
          value19: '',
          value20: '',
          dictName: row.dictName,
          sort: 1,
          remark: '',
          isAdd: true,
        }
      },
      async loadDataById(row) {
        this.formloading = true
        const queryData = {}
        queryData.dictId = row.id
        await getDictExtList(queryData).then((response) => {
          const dictExt = response.result[0]
          this.formData = Object.assign({ isAdd: false }, dictExt)
          if (!this.formData.id) {
            this.initForm(row)
            this.formData.dictId = row.id
          }
          this.formData.dictName = row.dictName
          this.formloading = false
        })
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveDictExt(this.formData)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
