<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >

        <el-form-item v-show="formData.type !== '2'" label="版本号" prop="version">
          <el-input v-model="formData.version" style="width:200px;" />
        </el-form-item>

        <el-form-item label="版本类型" prop="remark">
          <el-select v-model="formData.type" placeholder="请选择" :disabled="!formData.isAdd">
            <el-option label="wgt" value="wgt"/>
            <el-option label="apk" value="apk"/>
          </el-select>
        </el-form-item>

        <el-form-item label="包文件" prop="path">
          <CommonUploadLargeFileFdfsFormItem style="width:564px" v-model="formData.path"
          v-if="formData.id"
          :uploadData="{bizId:formData.id,bizCode:'appVersion'}" />
        </el-form-item>

        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 10}"
            placeholder="请输入内容(不超过1000字)"
            maxlength="1000"
            style="width:564px"
          />
        </el-form-item>

      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import CommonUploadLargeFileFdfsFormItem from '../common/CommonUploadLargeFileFdfsFormItem'
  import { saveAppVersion } from '@/api/system/syAppVersion-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  export default {
    name: 'appVersionEdit',
    components: {CommonUploadLargeFileFdfsFormItem},
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          version: [
            { required: true, message: '版本号为必填', trigger: 'blur' }
          ],
          path: [
            { required: true, message: '包文件', trigger: 'blur' }
          ],
        },
        queryMap: {
          id:'',
          name:'',
          code:''
        },
      }
    },
    watch: {

    },
    created() {

    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增版本'
          this.initForm()
        } else {
          this.title = '编辑版本'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          name: '',
          parentId: '-1',
          path: '',
          pcMenuId:'',
          imgUrl: '',
          sort: '',
          remark: '',
          isAdd: true
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveAppVersion(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ? '新增版本成功！' : '修改版本成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ?'新增版本失败！' : '修改版本失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
