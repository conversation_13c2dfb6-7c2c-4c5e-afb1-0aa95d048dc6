<template>
  <el-dialog
    v-drag
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
    append-to-body
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="机构编码" prop="departCode">
          <el-input v-model="formData.departCode" style="width:200px;" />
        </el-form-item>
        <el-form-item label="机构简称" prop="departName">
          <el-input v-model="formData.departName" style="width:200px;" />
        </el-form-item>
        <el-form-item label="机构全称" prop="departFullName">
          <el-input v-model="formData.departFullName" style="width:200px;" />
        </el-form-item>
        <el-form-item label="机构类型" prop="departType">
          <el-radio-group v-model="formData.departType" size="small" style="width:200px;">
            <el-radio-button label="0">公司</el-radio-button>
            <el-radio-button label="1">子公司</el-radio-button>
            <el-radio-button label="2">部门</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="上级机构" prop="parentDepartName">
          <el-input
            v-model="formData.parentDepartName"
            style="width:200px;"
            readonly
            @click.native="showDepartDialog"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="manager">
          <el-input v-model="formData.manager" style="width:200px;" />
        </el-form-item>
        <el-form-item label="序号" prop="sort">
          <el-input-number
            v-model.number="formData.sort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width:200px;"
          />
        </el-form-item>
        <el-form-item label="机构地址" prop="address">
          <el-input
            v-model="formData.address"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            style="width:200px;"
          />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 4}"
            style="width:200px;"
          />
        </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <CommonDepartPopup ref="CommonDepartPopup" @callBackDepart="callBackDepart" />
  </el-dialog>
</template>

<script>
  import {
    getAllDeparts,
    delDepartById,
    saveDepart,
    getDepartsByPage,
    getDepartById,
    delBatchDepart
  } from '@/api/system/depart-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import CommonDepartPopup from '../common/CommonDepartPopup'

  export default {
    name: 'TableEdit',
    components: { CommonDepartPopup },
    props:{
      parent:{
        type: Object,
        default: () => {},
      }
    },
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          departName: [{ required: true, message: '机构名称为必填', trigger: 'blur' }],
          departCode: [{ required: true, message: '机构编码为必填', trigger: 'blur' }]
        },
      }
    },
    created() {
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增机构'
          this.initForm()
        } else {
          this.title = '编辑机构'
          this.loadDataById(row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      showDepartDialog() {
        this.$refs.CommonDepartPopup.showDepartDialog([this.formData.parentId],false,true)
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          departCode: '',
          departName: '',
          departFullName: '',
          departType: '0',
          parentId: this.parent.parentId,
          parentDepartName:  this.parent.parentDepartName,
          address: '',
          manager: '',
          sort: '',
          state: '1',
          remark: '',
          isAdd: true
        }
      },
      async loadDataById(row) {
        this.formloading = true
        getDepartById(row.id).then(response => {
          const depart = response.result
          this.formData = Object.assign({ isAdd: false,parentDepartName:'',parentId:'' }, depart)
          this.formData.parentDepartName = this.parent.parentDepartName
          this.formloading = false
        })
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveDepart(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ?  '新增机构成功！' : '修改机构成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ?'新增机构失败！' : '修改机构失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      callBackDepart(selectDatas) {
        this.formData.parentId = selectDatas[0].id
        this.formData.parentDepartName = selectDatas[0].label
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
