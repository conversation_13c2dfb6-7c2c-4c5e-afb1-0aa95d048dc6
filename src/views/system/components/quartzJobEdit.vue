<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="1350px"
    @close="close"
    top="5vh"
    center
    v-drag
  >
 <el-form
     ref="dataForm"
     v-loading="formloading"
     :inline="true"
     :rules="rules"
     :model="formData"
     label-position="right"
     label-width="130px"
   >

   <table v-loading="formloading" class="form-table" >
     <tr class="title"><td colspan="3">基本信息</td></tr>
   <tr>
       <td>
           <el-form-item label="调度名称" prop="jobName">
             <el-input v-model="formData.jobName"  style="width:280px;"/>
           </el-form-item>
       </td>
       <td>
           <el-form-item label="执行类" prop="jobBean">
             <el-input v-model="formData.jobBean" style="width:280px;"/>
           </el-form-item>
       </td>
       <td>
           <el-form-item label="执行方法" prop="methodName">
             <el-input v-model="formData.methodName" style="width:280px;"/>
           </el-form-item>
       </td>
    </tr>
    <tr>
        <td>
            <el-form-item label="周期表达式" prop="jobCron">
              <el-input v-model="formData.jobCron" style="width:280px;"/>
            </el-form-item>
        </td>
        <td>
            <el-form-item label="任务参数" prop="jobParam">
             <el-input
               v-model="formData.jobParam"
               maxlength="1000"
               placeholder=""
               style="width: 280px"
               type="textarea"
             />
            </el-form-item>
        </td>
        <td>
            <el-form-item label="是否暂停" prop="isPause">
              <el-radio-group v-model="formData.isPause" size="small" style="width:200px;">
                <el-radio-button label='1'>是</el-radio-button>
                <el-radio-button label='0'>否</el-radio-button>
              </el-radio-group>
            </el-form-item>
        </td>
     </tr>
     <tr>
         <td>
          <el-form-item label="开始日期" prop="startTime">
           <el-date-picker
               type="datetime"
               v-model="formData.startTime"
               format="yyyy-MM-dd hh:mm:ss"
               placeholder="选择时间"
               value-format="yyyy-MM-dd hh:mm:ss"
               style="width:280px;"
             />

           </el-form-item>


         </td>
         <td>
          <el-form-item label="结束日期" prop="endTime">
             <el-date-picker
               type="datetime"
               v-model="formData.endTime"
               format="yyyy-MM-dd hh:mm:ss"
               placeholder="选择时间"
               value-format="yyyy-MM-dd hh:mm:ss"
               style="width:280px;"
             />
           </el-form-item>
         </td>
         <td >
             <el-form-item label="备注" prop="remark">
              <el-input
                v-model="formData.remark"
                maxlength="1000"
                placeholder="请输入内容(不超过1000字)"
                style="width: 280px"
                type="textarea"
              />
             </el-form-item>
         </td>
      </tr>

  </table>
    </el-form>

    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>

  </el-dialog>
</template>

<script>
  import {saveQuartzJob} from '@/api/system/quartzJob-api.js'
  import { genUUID } from '@/utils/th_utils.js'
  import { mapActions, mapGetters } from 'vuex'
  import { parseTime } from '@/utils/index'
  import tableMix from '@/views/mixins/table'
  export default {
    name: 'TableEdit',
    components: {},
    props: {},
    mixins: [tableMix],
    data() {

      return {
        structureTypeList: [],
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          jobName: [
            { required: true, message: '调度名称为必填', trigger: 'blur' },
          ],
          jobBean: [
            { required: true, message: '执行类为必填', trigger: 'blur' },
          ],
          methodName: [
            { required: true, message: '执行方法为必填', trigger: 'blur' },
          ],
          jobCron: [
            { required: true, message: '周期表达式为必填', trigger: 'blur' },
          ]
        },
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
        username: 'user/username',
      }),
    },
    created() {},
    methods: {
      	changeLog(e) {
      				console.log('change事件:', e);
      			},
      showEdit(row) {
        if (!row) {
          this.title = '新增定时任务'
          this.initForm()
        } else {
          this.title = '编辑定时任务'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          createBy:this.userid, //创建人id
          createTime:parseTime(new Date()), //创建时间
          updateBy:'', //修改人id
          updateTime:'', //修改时间
          endTime:'', //结束时间
          isPause: '1', //是否暂停
          jobBean: '', //执行类
          jobCron: '', //周期表达式
          jobName: '', //调度名称
          jobParam:'', //任务参数
          methodName: '', //执行方法
          remark: '', //备注
          startTime: '', //开始日期
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveQuartzJob(this.formData)
              .then((response) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增定时任务成功！' : '修改定时任务成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch((err) => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增定时任务失败！' : '修改定时任务失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },

    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
