<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="formData.type" style="width:500px;">
            <el-radio label="0">目录</el-radio>
            <el-radio label="1">菜单</el-radio>
          </el-radio-group>
        </el-form-item>

       <el-form-item label="上级项目" prop="parentId">
          <t-form-tree v-model="formData.parentId" :treeData="treeData"
          :defaultProps="{children: 'children',label: 'name'}"
          style="width:535px;"
          />
        </el-form-item>

        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="formData.name" style="width:200px;" />
        </el-form-item>

        <el-form-item label="序号" prop="sort">
          <el-input-number
            v-model.number="formData.sort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width:200px;"
          />
        </el-form-item>

        <el-form-item label="菜单图标" prop="icon" v-if="formData.type === '1'" >
          <el-input v-model="formData.icon" style="width:475px;" disabled />
          <el-button icon="el-icon-search" type="primary" @click="showCommonGKUploadFile"></el-button>
        </el-form-item>

        <el-form-item label="页面地址" prop="path" v-if="formData.type === '1'" >
          <el-input v-model="formData.path" style="width:535px;" />
        </el-form-item>

        <el-form-item label="PC端code" prop="pcMenuId">
          <t-form-tree v-model="formData.pcMenuId" :treeData="pcTreeData"
          :defaultProps="defaultProps"
          style="width:535px;"/>
        </el-form-item>

        <el-form-item label="描述" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:200px;" />
        </el-form-item>

      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <CommonGKUploadFile  ref="CommonGKUploadFile" @callBackData="callBackData"/>
  </el-dialog>
</template>

<script>
  import { getMenuList } from '@/api/system/menu-api'
  import { saveAppMenu, getAppMenuList, checkAppMenu } from '@/api/system/syAppMenu-api'
  import { genUUID, parseTime } from '@/utils/th_utils.js'
  import tFormTree from '../common/t-form-tree.vue'
  import CommonGKUploadFile from '../common/CommonGKUploadFile'

  export default {
    name: 'AppMenuEdit',
    components: { tFormTree,CommonGKUploadFile },
    props:{
      treeData:{
        type: Array,
        default: () => []
      }
    },
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        pcTreeData:[],
        rules: {
          name: [
            { required: true, message: '名称为必填', trigger: 'blur' }
          ],
          icon: [
            { required: true, message: '菜单图标为必填', trigger: 'change' }
          ],
          path: [
            { required: true, message: '页面地址为必填', trigger: 'blur' }
          ]
        },
      }
    },
    watch: {

    },
    computed: {
      defaultProps(){
        return {
          children: 'children',
          label(data, node){
            if(data.id=='-1'){
              return '根目录'
            }else{
              return data.name+' [ '+data.code+' ] '
            }
          }
        }
      }
    },
    created() {
      this.getMenuList()
    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增菜单'
          this.initForm()
        } else {
          this.title = '编辑菜单'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          name: '',
          parentId: '-1',
          path: '',
          type: '0',
          pcMenuId:'',
          icon: '',
          sort: '',
          remark: '',
          isAdd: true
        }
      },
      getMenuList() {
        getMenuList({}).then(response => {
          this.pcTreeData =response.result.filter((item)=>{return item.type!='2'})
        })
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveAppMenu(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ? '新增菜单成功！' : '修改菜单成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ?'新增菜单失败！' : '修改菜单失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      showCommonGKUploadFile(){
        this.$refs.CommonGKUploadFile.show()
      },
      callBackData(val){
        if(val.length>0){
          this.formData.icon='/'+val[0].urlPath
        }
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
