<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="序号" prop="sort">
        <el-input-number
          v-model.number="formData.sort"
          controls-position="right"
          :max="999"
          :min="0"
          style="width: 200px"
        />
      </el-form-item>


      <el-form-item label="业务编码" prop="businessCode">
        <el-input v-model="formData.businessCode" style="width: 200px" />
      </el-form-item>
      <el-form-item label="生成规则" prop="generatingRule">
        <el-input v-model="formData.generatingRule" style="width: 575px" />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          maxlength="1000"
          placeholder="请输入内容(不超过1000字)"
          style="width: 575px"
          type="textarea"
        />
      </el-form-item>
      <el-alert
        show-icon
        style="width: 575px; margin-left: 120px"
        :title="'功能介绍：' + introduce"
        type="success"
      />
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { genUUID } from '@/utils/th_utils.js'
  // import { getDictDetailsByCodes } from '@/api/system/dict-api'
  import {
    saveTemplateManagement,
    checkTemplateManagement,
  } from '@/api/system/templateManagement-api'

  export default {
    name: 'templateManagementEdit',
    components: {},
    props: {},
    data() {
      const validateBusinessCode = (rule, value, callback) => {
        if (!(this.formData.isAdd)) {
          callback()
          return
        }
        // checkTemplateManagement({businessCode:value}).then((response) => {
        //   const result = response
        //   if (result.code === 200) {
        //     callback()
        //   } else {
        //     callback(new Error('业务编码已存在!'))
        //   }
        // })
        checkTemplateManagement({businessCode:value}).then(response => {
          callback()
        }).catch((err)=>{
          callback(new Error('业务编码已存在!'))
        })
      }
      return {
        title: '',
        introduce:
          '生成规则中按照${key}的方式配置,使用时传入对应的类型,系统编码,业务编码加上配置的key即可生成对应的编码',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        visible: false,
        rules: {
          businessCode: [
            { required: true, message: '业务编码为必填', trigger: 'blur' },
            {
              required: true,
              validator: validateBusinessCode,
              trigger: 'blur',
            },
          ],
          generatingRule: [
            { required: true, message: '生成规则为必填', trigger: 'blur' },
          ],
        },
      }
    },
    created() {
      // this.getDictDetails()
    },
    methods: {
      async getDictDetails() {
        // await getDictDetailsByCodes('systemCode,templateType').then(
        //   (response) => {
        //     this.selectedSystemCode = []
        //     const systemCode = response.result.systemCode
        //     for (const data of systemCode) {
        //       const treeNode = {}
        //       treeNode.id = data.id
        //       treeNode.label = data.dictName
        //       this.selectedSystemCode.push(treeNode)
        //     }
        //     const templateType = response.result.templateType
        //     for (const data of templateType) {
        //       const treeNode = {}
        //       treeNode.id = data.id
        //       treeNode.label = data.dictName
        //       this.selectedType.push(treeNode)
        //     }
        //   }
        // )
      },
      showEdit(row) {
        if (!row) {
          this.title = '新增模板'
          this.initForm()
        } else {
          this.title = '编辑模板'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          type: '',
          systemCode: '',
          businessCode: '',
          generatingRule: '',
          remark: '',
          createBy: '',
          createTime: '',
          updateBy: '',
          updateTime: '',
          sort: '',
          isAdd: true,
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveTemplateManagement(this.formData)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增模板成功！' : '修改模板成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增模板失败！' : '修改模板失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
