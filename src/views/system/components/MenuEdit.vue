<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="formData.type !== '2' ? rules : rules2"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="formData.type" style="width:500px;">
            <el-radio label="0">目录</el-radio>
            <el-radio label="1">菜单</el-radio>
            <el-radio label="2">功能权限</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="formData.type !== '2'" label="菜单图标" prop="icon">
          <el-popover
            popper-class="icon-selector-popper"
            width="292"
            trigger="click"
          >
            <vab-icon-selector  @handle-icon="handleIcon" />
            <el-input
              slot="reference"
              v-model="formData.icon"
              style="width:200px;"
              placeholder="点击选择图标"
              readonly
            >
              <vab-icon-mix style="color:#000;"  slot="prefix" v-if="formData.icon" :icon="formData.icon" />
              <i v-else slot="prefix" class="el-icon-search el-input__icon" />
            </el-input>
          </el-popover>
        </el-form-item>
        <el-form-item v-show="formData.type !== '2'" label="菜单标题" prop="name">
          <el-input v-model="formData.name" style="width:200px;" />
        </el-form-item>
        <el-form-item v-show="formData.type !== '2'" label="菜单编码" prop="code">
          <el-input v-model="formData.code" style="width:200px;" />
        </el-form-item>
        <el-form-item v-show="formData.type !== '2'" label="路由地址" prop="path">
          <el-input v-model="formData.path" style="width:200px;" />
        </el-form-item>
        <el-form-item v-show="formData.type === '2'" label="权限名称" prop="name">
          <el-input v-model="formData.name" style="width:200px;" />
        </el-form-item>
        <el-form-item v-show="formData.type === '2'" label="权限标识" prop="permissions">
          <el-input v-model="formData.permissions" style="width:200px;" />
        </el-form-item>
        <el-form-item v-show="formData.type === '1'" label="组件名称" prop="componentName">
          <el-input v-model="formData.componentName" style="width:200px;" />
        </el-form-item>
        <el-form-item label="序号" prop="sort">
          <el-input-number
            v-model.number="formData.sort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width:200px;"
          />
        </el-form-item>
        <el-form-item label="上级项目" prop="parentId">
          <el-popover
            v-model="visible"
            placement="bottom-start"
            width="300"
            trigger="click"
          >
            <el-input v-model="filterText" clearable placeholder="输入关键字进行过滤" style="margin-bottom: 10px;" />
            <el-tree
              ref="formMenuTree"
              class="dropTree"
              :data="treeData"
              node-key="id"
              :filter-node-method="filterNode"
              :default-expanded-keys="[-1]"
              highlight-current
              :expand-on-click-node="false"
              :props="defaultProps"
              @node-click="handleNodeClick"
            />
            <div style="text-align: center; margin-top:10px;">
              <el-button type="primary" size="mini" @click="visible = false">确定</el-button>
            </div>
            <el-input slot="reference" v-model="formData.parentName" placeholder="请选择上级" readonly style="width:200px" />
          </el-popover>
        </el-form-item>
        <el-form-item v-show="formData.type === '1'" label="缓存" prop="cache">
          <el-radio-group v-model="formData.cache" size="small" style="width:200px;">
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="formData.type !== '2'" label="是否可见" prop="state">
          <el-radio-group v-model="formData.state" size="small" style="width:200px;">
            <el-radio-button label="0">可见</el-radio-button>
            <el-radio-button label="1">隐藏</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-show="formData.type === '1'" label="组件路径" prop="component">
          <el-input v-model="formData.component" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:200px;" />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:200px;" />
        </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { checkMenu, saveMenu } from '@/api/system/menu-api'
  import VabIconSelector from '@/extra/VabIconSelector'
  import { genUUID } from '@/utils/th_utils.js'

  export default {
    name: 'TableEdit',
    components: { VabIconSelector },
    props:{
      treeData: {
        type: Array,
        default: () => [],
      }
    },
    data() {
      const validateMenuCode = (rule, value, callback) => {
        if (!(this.formData.isAdd)) {
          callback()
          return
        }
        // 校验菜单编码是否存在!
        checkMenu({code:value}).then(response => {
          callback()
        }).catch(err=>{
          callback(new Error('菜单编码已存在!'))
        })
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label'
        },
        visible: false,
        dictIsAdd: false,
        rules: {
          name: [
            { required: true, message: '菜单名称为必填', trigger: 'blur' }
          ],
          code: [
            { required: true, message: '菜单编码为必填', trigger: 'blur' },
            { required: true, validator: validateMenuCode, trigger: 'blur' }
          ]
        },
        rules2: {
          name: [
            { required: true, message: '权限名称为必填', trigger: 'blur' }
          ],
          permissions: [
            { required: true, message: '权限标识为必填', trigger: 'blur' }
          ]
        },

      }
    },
    watch: {
      filterText(val) {
        this.$refs.formMenuTree.filter(val)
      }
    },
    created() {},
    methods: {
      handleIcon(item) {
        this.formData.icon = item
      },
      handleNodeClick(node) {
        this.formData.parentId = node.id
        this.formData.parentName = node.label
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      showEdit(row) {
        if (!row) {
          this.title = '新增菜单'
          this.initForm()
        } else {
          this.title = '编辑菜单'
          // console.log(row)
          this.formData = Object.assign({ isAdd: false, parentName: '全部', }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
          const selectNode = this.$refs.formMenuTree.getNode(this.formData.parentId)
          if (selectNode != null) {
            this.formData.parentName = selectNode.label
          }
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          name: '',
          type: '0',
          state: '0',
          cache: '1',
          permissions: '',
          path: '',
          componentName: '',
          component: '',
          parentId: '-1',
          parentName: '全部',
          icon: '',
          sort: '',
          remark: '',
          isAdd: true
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveMenu(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ? '新增菜单成功！' : '修改菜单成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ?'新增菜单失败！' : '修改菜单失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
