<template>
  <el-dialog
    v-drag
    append-to-body
    :close-on-click-modal="false"
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
  >
    <el-form
      ref="dataForm"
      v-loading="formloading"
      :inline="true"
      label-position="right"
      label-width="120px"
      :model="formData"
      :rules="rules"
    >
      <el-form-item label="序号" prop="sort">
        <el-input-number
          v-model.number="formData.sort"
          controls-position="right"
          :max="999"
          :min="0"
          style="width: 200px"
        />
      </el-form-item>
      <el-form-item label="字典名称" prop="dictName">
        <el-input v-model="formData.dictName" style="width: 200px" />
      </el-form-item>
      <el-form-item label="字典编码" prop="dictCode">
        <el-input v-model="formData.dictCode" style="width: 200px" />
      </el-form-item>
      <el-form-item label="字典全称" prop="dictFullName">
        <el-input v-model="formData.dictFullName" style="width: 200px" />
      </el-form-item>
      <el-form-item label="上级字典" prop="parentId">
        <el-popover
          v-model="visible"
          placement="bottom-start"
          trigger="click"
          width="300"
        >
          <el-input
            v-model="filterText"
            clearable
            placeholder="输入关键字进行过滤"
            style="margin-bottom: 10px"
          />
          <el-tree
            ref="dictTree"
            class="dropTree"
            :data="treeDict"
            default-expand-all
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            highlight-current
            node-key="id"
            :props="defaultProps"
            @node-click="handleNodeClick"
          />
          <div style="margin-top: 10px; text-align: center">
            <el-button size="mini" type="primary" @click="visible = false">
              确定
            </el-button>
          </div>
          <el-input
            slot="reference"
            v-model="formData.parentName"
            placeholder="请选择上级"
            readonly
            style="width: 200px"
          />
        </el-popover>
      </el-form-item>
      <el-form-item label="描述" prop="remark">
        <el-input
          v-model="formData.remark"
          style="width: 530px"
          type="textarea"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import {
    checkDictCode,
    saveDict,
    getSysDictById,
  } from '@/api/system/dict-api'
  import { genUUID } from '@/utils/th_utils.js'

  export default {
    name: 'TableEdit',
    components: {},
    props: {},
    data() {
      const validateRoleCode = (rule, value, callback) => {
        if (!this.formData.isAdd) {
          callback()
          return
        }
        // 校验字典编码是否存在!
        checkDictCode(value, this.formData.parentId)
          .then(() => {
            callback()
          })
          .catch(() => {
            callback(new Error('字典编码已存在!'))
          })
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        treeDict: [],
        filterText: '',
        defaultProps: {
          children: 'children',
          label: 'label',
        },
        visible: false,
        rules: {
          dictName: [
            { required: true, message: '字典名称为必填', trigger: 'blur' },
          ],
          dictCode: [
            { required: true, message: '字典编码为必填', trigger: 'blur' },
            { required: true, validator: validateRoleCode, trigger: 'blur' },
          ],
        },
      }
    },
    watch: {
      filterText(val) {
        this.$refs.dictTree.filter(val)
      },
    },
    created() {},
    methods: {
      showEdit(row, data) {
        this.treeDict = data.treeDict
        if (!row) {
          this.title = '新增字典'
          this.initForm()
        } else {
          this.title = '编辑字典'
          console.log(row)
          this.loadDataById(row)
        }
        this.dialogFormVisible = true
        this.$nextTick(() => {
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          dictName: '',
          dictFullName: '',
          parentId: '-1',
          parentName: '全部',
          dictCode: '',
          sort: 1,
          remark: '',
          isAdd: true,
        }
      },
      async loadDataById(row) {
        this.formloading = true
        await getSysDictById(row.id).then((response) => {
          const dict = response.result
          this.formData = Object.assign({ isAdd: false, parentName: '' }, dict)
          this.formData.parentName = this.$refs.dictTree.getNode(
            this.formData.parentId
          ).label
          this.formloading = false
        })
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveDict(this.formData)
              .then(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增成功！' : '修改成功!',
                  'success',
                  'vab-hey-message-success'
                )
                this.close()
                this.$emit('fetch-data')
              })
              .catch(() => {
                this.$baseMessage(
                  this.formData.isAdd ? '新增失败！' : '修改失败!',
                  'error',
                  'vab-hey-message-error'
                )
              })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      filterNode(value, data) {
        if (!value) return true
        return data.label.indexOf(value) !== -1
      },
      handleNodeClick(node) {
        this.formData.parentId = node.id
        this.formData.parentName = node.label
        this.$forceUpdate()
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree {
    width: 100%;
    max-height: 300px;
    overflow: auto;
  }
</style>
