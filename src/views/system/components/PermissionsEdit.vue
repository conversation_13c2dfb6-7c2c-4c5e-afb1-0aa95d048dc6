<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" style="width:200px;" />
        </el-form-item>
        <el-form-item label="编码" prop="code">
          <el-input v-model="formData.code" style="width:200px;" />
        </el-form-item>
        <el-form-item label="权限" prop="menus">
          <el-input v-model="formData.menus" readonly @click.native="showMenusDialog" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;" />
        </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <CommonMenusPopup ref="CommonMenusPopup" @callBackMenus="callBackMenus"></CommonMenusPopup>
  </el-dialog>
</template>

<script>
  import { savePermissions, getPermissionsList } from '@/api/system/permissions-api'
  import { genUUID } from '@/utils/th_utils.js'
  import { parseTime } from '@/utils/index'
  import CommonMenusPopup from '../common/CommonMenusPopup'
  import { mapActions, mapGetters } from 'vuex'

  export default {
    name: 'TableEdit',
    components: {
      CommonMenusPopup
    },
    props:{},
    data() {
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {},
        permissionsList: [],
        permissions: [],
        relId:'',
        type:''
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
        username: 'user/username',
      }),
    },
    created() {

    },
    methods: {
      async showEdit(row,type) {
        this.title = '绑定权限'
        this.formData = Object.assign({ menus:'' }, row)
        this.relId = row.id
        this.type = type
        await getPermissionsList({relId: row.id,type: type}).then(response=>{
          // console.log(response)
          this.permissionsList = response.result
          this.formData.menus = this.permissionsList.map(item=>item.permissionsName).join(',')
        })
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      callBackMenus(data){
        // console.log(data)
        this.formData.menus = data.map(item=>item.label).join(',')
        this.permissions = data.map(item=>item.id)
      },
      showMenusDialog(){
        var ids = this.permissionsList.map(item=>item.permissionsId)
        this.$refs.CommonMenusPopup.showMenusDialog(ids)
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            var data = {
              permissionsList:[],
              relId: this.relId,
              type: this.type
            }
            this.permissions.forEach((item,index)=>{
              let obj = {
                createBy: this.userid,
                createByName: this.username,
                createTime: parseTime(new Date()),
                id: genUUID(),
                permissions: item,
                relId: this.relId,
                type: this.type,
                sort: index
              }
              data.permissionsList.push(obj)
            })
            savePermissions(data).then(response => {
              this.$baseMessage(
                '绑定权限成功！',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                '绑定权限失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
