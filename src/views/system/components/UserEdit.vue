<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item v-if="formData.isAdd" label="用户账号" prop="userAccount">
          <el-input v-model="formData.userAccount" style="width:200px;" />
        </el-form-item>
        <el-form-item v-if="formData.isAdd" label="密码" prop="userPwd">
          <el-input v-model="formData.userPwd" style="width:200px;" />
        </el-form-item>
        <el-form-item label="用户姓名" prop="userName">
          <el-input v-model="formData.userName" style="width:200px;" />
        </el-form-item>
        <el-form-item label="年龄" prop="age">
          <el-input v-model="formData.age" style="width:200px;" />
        </el-form-item>
        <el-form-item label="手机号" prop="telephone">
          <el-input v-model="formData.telephone" style="width:200px;" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group @change="changeGender" v-model="formData.gender" size="small" style="width:200px;">
            <el-radio-button label="1">男</el-radio-button>
            <el-radio-button label="0">女</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="允许重复登录" prop="repeatLogin">
          <el-radio-group @change="changeRepeatLogin" v-model="formData.repeatLogin" size="small" style="width:200px;">
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="formData.email" style="width:200px;" />
        </el-form-item>
        <el-form-item label="角色" prop="roleNames">
          <el-input v-model="formData.roleNames" readonly @click.native="showRoleDialog" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;" />
        </el-form-item>
        <el-form-item label="所在部门" prop="departNames">
          <el-input v-model="formData.departNames" readonly @click.native="showDepartDialog" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:500px;" />
        </el-form-item>
        <el-form-item label="是否启用" prop="state">
          <el-radio-group @change="changeState" v-model="formData.state" size="small" style="width:200px;">
            <el-radio-button label="1">是</el-radio-button>
            <el-radio-button label="0">否</el-radio-button>
          </el-radio-group>
        </el-form-item>
       <el-form-item label="有效时间" prop="effectiveTime">
         <el-date-picker
           v-model="formData.effectiveTime"
           format="yyyy-MM-dd"
           placeholder="选择时间"
           value-format="yyyy-MM-dd"
         />
       </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
    <CommonDepartPopup ref="CommonDepartPopup" @callBackDepart="callBackDepart" />
    <CommonRolePopup ref="CommonRolePopup" @callBackRole="callBackRole"></CommonRolePopup>
  </el-dialog>
</template>

<script>
  import { checkUser, saveUser } from '@/api/system/user-api'
  import CommonDepartPopup from '../common/CommonDepartPopup'
  import CommonRolePopup from '../common/CommonRolePopup'
  import { genUUID } from '@/utils/th_utils.js'
  import { parseTime } from '@/utils/index'
  import { mapActions, mapGetters } from 'vuex'

  export default {
    name: 'TableEdit',
    components: { CommonDepartPopup, CommonRolePopup },
    props:{},
    data() {
      const validateUserAccount = (rule, value, callback) => {
        if (!(this.formData.isAdd)) {
          callback()
          return
        }
        // 校验用户是否存在!
        checkUser({userAccount:value}).then(response => {
          callback()
        }).catch((err)=>{
          callback(new Error('用户账号已存在!'))
        })
      }
      const validatePassword = (rule, value, callback) => {
        const pattern = /^(?![A-Za-z]+$)(?![A-Z\d]+$)(?![A-Z\W]+$)(?![a-z\d]+$)(?![a-z\W]+$)(?![\d\W]+$)\S{8,}$/
        if (value && !pattern.test(value)) {
          callback(new Error('请输入8位以上大小写英文字母、数字或者符号，且大小写英文字母、数字和标点符号至少包含三种'))
          return
        } else {
          callback()
        }
      }
      const validateTelephone = (rule, value, callback) => {
        let pattern = /^1(3|4|5|7|8|6|9)\d{9}$/
        if (!pattern.test(value)) {
          callback(new Error('请输入正确的手机号!'))
          return
        }
        let data={telephone:value,isAdd:this.formData.isAdd}
        if (!(this.formData.isAdd)) {
          data.id=this.formData.id
        }
        // 校验手机号是否存在!
        checkUser(data).then(response => {
          callback()
        }).catch((err)=>{
          callback(new Error('手机号已存在!'))
        })
      }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          userAccount: [
            { required: true, message: '用户账号为必填', trigger: 'blur' },
            { required: true, validator: validateUserAccount, trigger: 'blur' }
          ],
          userPwd: [
            { required: true, message: '密码为必填', trigger: 'blur' },
            { required: true, validator: validatePassword, trigger: 'blur' }
          ],
          userName: [
            { required: true, message: '用户姓名为必填', trigger: 'blur' },
          ],
          departNames: [
            { required: true, message: '部门为必选', trigger: 'change' },
          ],
          roleNames: [
            { required: true, message: '角色为必选', trigger: 'change' },
          ],
          telephone: [
            { required: true, message: '手机号为必填', trigger: 'blur'},
            { required: true, validator: validateTelephone, trigger: 'blur' }
          ],
          email: [
            { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
          ]
        },
      }
    },
    computed: {
      ...mapGetters({
        userid: 'user/userid',
        username: 'user/username',
      }),
    },
    created() {

    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增用户'
          this.initForm()
        } else {
          this.title = '编辑用户'
          this.formData = Object.assign({ isAdd: false,departNames:'',roleNames:'' }, row)
          this.formData.departNames = this.formData.departList.map(item=>item.departName).join(',')
          this.formData.roleNames = this.formData.roleList.map(item=>item.roleName).join(',')
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          userAccount: '',
          userPwd: '',
          userName: '',
          age:'',
          gender:'0',
          genderName:'女',
          telephone: '',
          email:'',
          roleList: [],
          roleNames:'',
          departList: [],
          departNames:'',
          repeatLogin: "1",
          repeatLoginName:'是',
          state:'1',
          stateName: "启用",
          effectiveTime:'',
          isAdd: true
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            this.formData.userRoleRelList = []
            this.formData.userDepartRelList = []
            this.formData.roleList.forEach((item,index)=>{
              let obj={
                id: genUUID(),
                userId: this.formData.id,
                roleId: item.id,
                createBy: this.userid,
                createByName: this.username,
                createTime: parseTime(new Date()),
                sort: index
              }
              this.formData.userRoleRelList.push(obj)
            })
            this.formData.departList.forEach((item,index)=>{
              let obj={
                id: genUUID(),
                userId: this.formData.id,
                departId: item.id,
                createBy: this.userid,
                createByName: this.username,
                createTime: parseTime(new Date()),
                sort: index
              }
              this.formData.userDepartRelList.push(obj)
            })
            saveUser(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ? '新增用户成功！' : '修改用户成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ? '新增用户失败！' : '修改用户失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
      showDepartDialog(){
        this.$refs.CommonDepartPopup.showDepartDialog(this.formData.departList.map(item=>item.id),true,false)//初始化勾选数据，是否复选，是否要根目录（-1）
      },
      callBackDepart(data){
        this.formData.departList = data
        this.formData.departNames = data.map(item=>item.label).join(',')
      },
      showRoleDialog(){
        this.$refs.CommonRolePopup.showRoleDialog(this.formData.roleList.map(item=>item.id),true)//初始化勾选数据，是否复选
      },
      callBackRole(data){
        this.formData.roleList = data
        this.formData.roleNames = data.map(item=>item.roleName).join(',')
      },
      changeGender(val){
        this.formData.genderName = this.formData.gender==='1'? '男' : '女'
        // console.log(this.formData.genderName)
      },
      changeRepeatLogin(val){
        this.formData.repeatLoginName = this.formData.repeatLogin==='1'? '是' : '否'
      },
      changeState(val){
        this.formData.stateName = this.formData.state==='1'? '启用' : '禁用'
      }
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
