<template>
  <el-dialog
    v-drag
    append-to-body
    :title="title"
    :visible.sync="dialogFormVisible"
    width="750px"
    @close="close"
    :close-on-click-modal="false"
  >
    <el-form
        ref="dataForm"
        v-loading="formloading"
        :inline="true"
        :rules="rules"
        :model="formData"
        label-position="right"
        label-width="120px"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="formData.roleName" style="width:200px;" />
        </el-form-item>
        <el-form-item label="角色编码" prop="roleCode">
          <el-input v-model="formData.roleCode" style="width:200px;" />
        </el-form-item>
        <el-form-item label="序号" prop="sort">
          <el-input-number
            v-model.number="formData.sort"
            :min="0"
            :max="999"
            controls-position="right"
            style="width:200px;"
          />
        </el-form-item>
        <el-form-item label="描述" prop="remark">
          <el-input v-model="formData.remark" type="textarea" :autosize="{ minRows: 2, maxRows: 4}" style="width:200px;" />
        </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="save">确认</el-button>
    </template>
  </el-dialog>
</template>

<script>
  import { saveRole, checkRole } from '@/api/system/role-api'
  import { genUUID } from '@/utils/th_utils.js'

  export default {
    name: 'TableEdit',
    components: {},
    props:{},
    data() {
      const validateRoleCode = (rule, value, callback) => {
      if (!(this.formData.isAdd)) {
        callback()
        return
      }
      // 校验用户是否存在!
      checkRole({roleCode:value}).then(response => {
        callback()
      }).catch((err)=>{
        callback(new Error('角色编码已存在!'))
      })
    }
      return {
        title: '',
        dialogFormVisible: false,
        formloading: false,
        formData: {},
        rules: {
          roleName: [
            { required: true, message: '角色名为必填', trigger: 'blur' }
          ],
          roleCode: [
            { required: true, message: '角色编码为必填', trigger: 'blur' },
            { required: true, validator: validateRoleCode, trigger: 'blur' }
          ]
        },
      }
    },
    created() {

    },
    methods: {
      showEdit(row) {
        if (!row) {
          this.title = '新增角色'
          this.initForm()
        } else {
          this.title = '编辑角色'
          this.formData = Object.assign({ isAdd: false }, row)
        }
        this.dialogFormVisible = true
        this.$nextTick(()=>{
          this.$refs['dataForm'].clearValidate()
        })
      },
      // 初始化表单
      initForm() {
        this.formData = {
          id: genUUID(),
          roleName: '',
          roleCode: '',
          remark: '',
          sort: '',
          isAdd: true
        }
      },
      save() {
        this.$refs['dataForm'].validate((valid) => {
          if (valid) {
            saveRole(this.formData).then(response => {
              this.$baseMessage(
                this.formData.isAdd ?  '新增角色成功！' : '修改角色成功!',
                'success', 'vab-hey-message-success'
              )
              this.close()
              this.$emit('fetch-data')
            }).catch(err => {
              this.$baseMessage(
                this.formData.isAdd ?'新增角色失败！' : '修改角色失败!' ,
                'error', 'vab-hey-message-error'
              )
            })
          }
        })
      },
      close() {
        this.$refs['dataForm'].resetFields()
        this.formData = this.$options.data().formData
        this.dialogFormVisible = false
      },
    },
  }
</script>

<style lang="scss" scoped>
  .dropTree{
    width: 100%;
    max-height:300px;
    overflow:auto;
  }
</style>
