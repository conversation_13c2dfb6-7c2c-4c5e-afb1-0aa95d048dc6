<template>
  <el-dialog v-drag append-to-body :title="title" :visible.sync="isShowDialog" width="1200px" top="5vh" :close-on-click-modal="false" :show-close="true" center>
    <el-row :gutter="10">
       <el-col :span="24">
        <el-form ref="searchForm" :inline="true" :model="queryData" class="demo-form-inline">
          <el-form-item label="文件名称" prop="fileName">
            <el-input v-model="queryData.fileName" placeholder="请输入名称" style="width: 200px" />
          </el-form-item>
          <el-button icon="el-icon-search" type="primary" @click.native="searchBannerInfoPage">搜索</el-button>
          <el-button icon="el-icon-refresh-right" @click.native="resetForm('searchForm')">重置</el-button>
          <el-button style="margin-left: 10px;margin-bottom: 10px;float: right" type="primary"  icon="el-icon-circle-plus" @click.native="addFile">
              新增
            </el-button>
        </el-form>

        <el-table
          ref="dataTable"
          v-loading="listLoading"
          :data="tableData"
          element-loading-text="加载中"
          max-height="490"
          border
          stripe
          class="tableList"
          size="medium"
          @selection-change="handleSelectionChange"
          @select="handleSelect"
        >
          <el-table-column
            type="selection"
            min-width="5%"
            align="center"
          />
          <el-table-column
            prop="fileName"
            label="文件名"
            min-width="15%"
            align="center"
          >
            <template v-slot="scope">
              <div v-if="/^(jpeg|png|jpg|bmp)$/.test(scope.row.fileExt)" >
                  <el-image
                  style="width: 50px; height: 50px"
                  fit="cover"
                  :src="scope.row.urlPath"
                  :preview-src-list="[scope.row.urlPath]">
                </el-image>

                <div>{{ scope.row.fileName }}</div>
              </div>
              <el-link type="primary" v-else-if="/^(pdf)$/.test(scope.row.fileExt)" :href="scope.row.urlPath" target="_blank" :underline="false" title="点击预览">
                {{ scope.row.fileName }}

              </el-link>
              <span v-else>{{ scope.row.fileName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="fileSize"
            label="文件大小(MB)"
            min-width="10%"
            align="center"
            :formatter="fileSizeFormat"
          />
          <el-table-column
            prop="fileExt"
            label="文件类型"
            min-width="10%"
            align="center"
          />
          <el-table-column
            prop="bizCode"
            label="业务编码"
            min-width="10%"
            align="center"
          />
          <el-table-column prop="userName" label="上传人" min-width="10%" align="center" />
          <el-table-column prop="createTime" label="上传时间" min-width="15%" align="center" />
          <el-table-column prop="urlPath" label="文件地址" min-width="20%" align="center">
            <template slot-scope="{row}">
              {{row.urlPath}}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="20%" align="center">
              <template slot-scope="{row}">
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  @click="deleteFile(row)"
                >删除</el-button>
              </template>
            </el-table-column>
        </el-table>
        <div style="margin-top:15px;text-align: left">
          <el-pagination
            :current-page="pageInfo.curPage"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageInfo.pageSize"
            background
            layout="total, sizes, prev, pager, next, jumper"
            :total="pageInfo.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-col>
    </el-row>
    <div slot="footer" class="dialog-footer">
      <el-button type="danger" @click="isShowDialog = false">关闭</el-button>
      <el-button type="primary" @click="confirmSelect">确定</el-button>
    </div>
      <CommonUploadLargeFileFdfsPopup ref="CommonUploadLargeFileFdfsPopup" class="uploadSlot" @refreshUploadFileList="refreshUploadFileList" />
  </el-dialog>
</template>

<script>
import CommonUploadLargeFileFdfsPopup from '@/views/system/common/CommonUploadLargeFileFdfsPopup'
import {
  getUploadFilesByPage,
  deleteFile,
} from '@/api/system/uploadFile-api'
export default {
  name: 'CommonUploadFile',
  components: {
    CommonUploadLargeFileFdfsPopup,
  },
  data() {
    return {
      listLoading: false,
      queryData: {
        fileName: '',
        bizId: '772b721a-45a3-11eb-9e74-0894ef72d9c4',
        bizCode: 'UploadSystem'
      },
      pageInfo: {
        curPage: 1,
        pageSize: 10,
        total: 100
      },
      tableData: [],
      isShowViewer:false,
      title: '系统图标库',
      isShowDialog: false,
      mulSelect: [],
    }
  },
  created() {
    this.getUploadFilesByPage()
  },
  methods: {
    fileSizeFormat(row, column) {
      const fileSize = row[column.property];
      const fileSizeMb = parseFloat(fileSize/1048576).toFixed(4);
      return fileSizeMb
    },
    initSearch() {
      this.queryData = {
        fileName: '',
        bizId: '772b721a-45a3-11eb-9e74-0894ef72d9c4',
        bizCode: 'UploadSystem'
      }
      this.pageInfo.curPage = 1
      this.pageInfo.pageSize = 10
      this.getUploadFilesByPage()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.getUploadFilesByPage()
    },
    addFile() {
      this.$refs.CommonUploadLargeFileFdfsPopup.showUploadLargeDialog({
        bizId: this.queryData.bizId,
        bizCode: this.queryData.bizCode,
      })
    },
    deleteFile(row) {
      this.$confirm('删除文件数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteFile(row.id).then(() => {
          this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success')
          this.refreshUploadFileList()
        }).catch(err=>{
          this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
        })
      })
    },
    async getUploadFilesByPage() {
      const curPage = this.pageInfo.curPage
      const pageSize = this.pageInfo.pageSize
      this.listLoading = true
      const { result: { records, total }} = await getUploadFilesByPage({...this.queryData, curPage, pageSize,})
      this.tableData = records
      this.pageInfo.total = parseInt(total)
      this.listLoading = false
    },
    searchBannerInfoPage() {
      var validation = true
      for (var value of Object.values(this.queryData)) {
        if (value !== '') {
          validation = false
          break
        }
      }
      if (validation) {
        this.$notify({
          title: '信息',
          message: '至少选填一个搜索条件！',
          type: 'error',
          duration: 2000
        })
      } else {
        this.pageInfo.curPage = 1
        this.getUploadFilesByPage()
      }
    },
    handleSizeChange(val) {
      this.pageInfo.pageSize = val
      this.pageInfo.curPage = 1
      this.refreshUploadFiles()
    },
    handleCurrentChange(val) {
      this.pageInfo.curPage = val
      this.refreshUploadFiles()
    },
    handleSelect(selection, row) {
      // 清除 所有勾选项
      this.$refs.dataTable.clearSelection()
      // 当表格数据都没有被勾选的时候 就返回
      // 主要用于将当前勾选的表格状态清除
      if (selection.length === 0) return
      this.$refs.dataTable.toggleRowSelection(row, true)

    },
    handleSelectionChange(val) {
        this.mulSelect = val
        val.forEach(item => {
          if (item) {
            if (val.length > 1) {
              this.$refs.dataTable.clearSelection()
              this.$refs.dataTable.toggleRowSelection(val.pop())
            }
          }
        })
    },
    async refreshUploadFiles() {
      await this.getUploadFilesByPage()
    },
    show() {
      this.isShowDialog = true
      this.initSearch()
    },
    confirmSelect() {
      this.$emit('callBackData', this.mulSelect)
      this.isShowDialog = false
    },
    refreshUploadFileList() {
      this.pageInfo.curPage = 1
      this.getUploadFilesByPage()
    }

  }
}
</script>

<style scoped lang="scss">

</style>
