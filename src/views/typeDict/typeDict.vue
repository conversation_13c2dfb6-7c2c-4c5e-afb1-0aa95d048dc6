<template>
  <div
    ref="custom-table"
    class="custom-table-container"
    :class="{ 'vab-fullscreen': isFullscreen }"
  >
    <vab-query-form>
      <vab-query-form-top-panel>
        <el-form
          ref="form"
          :inline="true"
          label-width="80px"
          :model="queryForm"
          @submit.native.prevent
        >
          <el-form-item label-width="80px" label="监测项:" class="postInfo-container-item" prop="dictCode">
            <el-select v-model="queryForm.dictCode" placeholder="选择监测项" filterable style="width:200px;">
              <el-option v-for="(item, i) in selectDeviceType" :key="i" :label="item.dictName" :value="item.dictCode" />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button
              icon="el-icon-search"
              native-type="submit"
              type="primary"
              @click="handleQuery"
            >
              查询
            </el-button>
            <el-button
              icon="el-icon-refresh-right"
              @click.native="resetForm('form')"
            >
              重置
            </el-button>
            <!--            <el-button type="text" @click="handleFold">
                          <span v-if="fold">展开</span>
                          <span v-else>合并</span>
                          <vab-icon
                            class="vab-dropdown"
                            :class="{ 'vab-dropdown-active': fold }"
                            icon="arrow-up-s-line"
                          />
                        </el-button> -->
          </el-form-item>
        </el-form>
      </vab-query-form-top-panel>
      <vab-query-form-left-panel :span="12">
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-button
          style="margin: 0 10px 10px 0 !important"
          type="primary"
          @click="clickFullScreen"
        >
          <vab-icon
            :icon="isFullscreen ? 'fullscreen-exit-fill' : 'fullscreen-fill'"
          />
          表格全屏
        </el-button>
        <el-popover
          ref="popover"
          popper-class="custom-table-checkbox"
          trigger="hover"
        >
          <el-radio-group v-model="lineHeight">
            <el-radio label="medium">大</el-radio>
            <el-radio label="small">中</el-radio>
            <el-radio label="mini">小</el-radio>
          </el-radio-group>
          <template #reference>
            <el-button style="margin: 0 10px 10px 0 !important" type="primary">
              <vab-icon icon="line-height" />
              表格尺寸
            </el-button>
          </template>
        </el-popover>
        <el-popover popper-class="custom-table-checkbox" trigger="hover">
          <el-checkbox-group v-model="checkList">
            <vab-draggable v-bind="dragOptions" :list="columns">
              <div v-for="(item, index) in columns" :key="item + index">
                <vab-icon icon="drag-drop-line" />
                <el-checkbox
                  :disabled="item.disableCheck === true"
                  :label="item.label"
                >
                  {{ item.label }}
                </el-checkbox>
              </div>
            </vab-draggable>
          </el-checkbox-group>
          <template #reference>
            <el-button
              icon="el-icon-setting"
              style="margin: 0 0 10px 0 !important"
              type="primary"
            >
              可拖拽列设置
            </el-button>
          </template>
        </el-popover>
      </vab-query-form-right-panel>
    </vab-query-form>

    <el-table
      ref="tableSort"
      v-loading="listLoading"
      border
      :data="list"
      :height="height"
      :size="lineHeight"
      stripe
      @selection-change="setSelectRows"
    >
    <el-table-column align="center" type="selection" width="55" />
                <el-table-column
                  label="序号"
                  type="index"
                  align="center"
                  width="50">
                </el-table-column>
      <!-- <el-table-column align="center" type="selection" width="55" /> -->
      <el-table-column
        v-for="(item, index) in finallyColumns"
        :key="index"
        :align="item.center ? item.center : 'center'"
        :label="item.label"
        :prop="item.prop"
        :sortable="item.sortable ? item.sortable : false"
        :width="item.width ? item.width : 'auto'"
      >
        <template #default="{row}">
          <span v-if="item.label == '通用单位'">
            {{ formatUnit(row.dictCode) }}
          </span>
          <span v-else-if="item.label == '通用标题'">
            {{ formatTitle(row.dictCode) }}
          </span>
          <span v-else-if="item.label == '纵坐标名称'">
            {{ formatName(row.dictCode) }}
          </span>
          <span v-else>
            {{ row[item.prop] }}
          </span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="555">
        <template #default="{ row }">
        <el-button icon="el-icon-edit" type="primary" @click="handleUnitConfig(row)" v-permissions="{permission:['deviceParams:update']}">
          单位配置
        </el-button>
          <el-button icon="el-icon-edit" type="primary" @click="handleLineTitleConfig(row)" v-permissions="{permission:['deviceParams:update']}">
            折线图标题配置
          </el-button>
          <el-button icon="el-icon-edit" type="primary" @click="handleLineYaxisConfig(row)" v-permissions="{permission:['deviceParams:update']}">
            折线图纵坐标名称配置
          </el-button>
        </template>
      </el-table-column>
      <!--<el-table-column align="center" label="操作" width="275">
        <template #default="{ row }">
          <el-button
            icon="el-icon-edit"
            style="margin: 0 10px 10px 0 !important"
            type="primary"
            @click="handleEdit(row) "
            v-permissions="{permission:['deviceParams:update']}"
          >
            编辑
          </el-button>
          <el-button
            icon="el-icon-delete"
            style="margin: 0 10px 10px 0 !important"
            type="danger"
            @click="handleDelete(row)"
            v-permissions="{permission:['deviceParams:del']}"
          >
            删除
          </el-button>
          <el-button icon="el-icon-view" style="margin: 0 10px 0 0 !important" type="success"
                     @click="handleDetail(row)">
            详情
          </el-button>
        </template>
      </el-table-column>-->
      <template #empty>
        <el-image
          class="vab-data-empty"
          :src="require('@/assets/empty_images/data_empty.png')"
        />
      </template>
    </el-table>
    <el-pagination
      background
      :current-page="pageInfo.curPage"
      :layout="layout"
      :page-size="pageInfo.pageSize"
      :total="pageInfo.total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
    <unit-config ref="unitConfig" @changeUnit="getDictDataList"/>
    <line-title-config ref="lineTitleConfig" @changeTitle="getDictDataList"/>
    <line-yaxis-config ref="lineYaxisConfig" @changeYaxisName="getDictDataList"/>
  </div>
</template>

<script>
import { getDictList } from '@/api/system/dict-api'
import { getDataListByPage } from '@/api/typeDict/typeDict-api'
import tableMix from '@/views/mixins/table'
import UnitConfig from './components/unitConfig.vue'
import lineTitleConfig from './components/lineTitleConfig.vue'
import lineYaxisConfig from './components/lineYaxisConfig.vue'

export default {
    name: 'user',
    components: {
        UnitConfig,
        lineTitleConfig,
        lineYaxisConfig
    },
    mixins: [tableMix],
    data() {
        return {
            selectDeviceType: [],
            dictDataList: [],
            queryData: {
                deviceType: ''
            },
            checkList: ['名称','编号','通用单位','通用标题', '纵坐标名称'],
            columns: [
                {
                    label: '名称',
                    prop: 'dictName',
                    disableCheck: true,
                },
                {
                    label: '编号',
                    prop: 'dictCode',
                    disableCheck: true,
                },
                {
                    label: '通用单位',
                    prop: 'unitCommon',
                    disableCheck: true,
                },
                {
                    label: '通用标题',
                    prop: 'titleCommon',
                    disableCheck: true,
                },
                {
                  label: '纵坐标名称',
                  prop: 'NameCommon',
                  width: '150'
                }
            ],
            queryForm: {
                projectId:sessionStorage.getItem('def_project_Id'),
                name:'',
                type: ''
            },
        }
    },
    computed: {
        finallyColumns() {
            return this.columns.filter((item) =>
                this.checkList.includes(item.label)
            )
        },
    },
    created() {
      this.getDictDataList()
        this.fetchData()
    },
    mounted() {
        this.projectId = sessionStorage.getItem('def_project_Id')
        this.getDictDetails()
    },
    methods: {
        // 获取桥对应的所有监测项
        // async getDeviceType() {
        //     const deviceTypeByProjectIdRes = await getDeviceTypeByProjectId({
        //         projectId: this.projectId,
        //     })
        //     this.selectDeviceType = deviceTypeByProjectIdRes.result
        //     // alert(this.selectDeviceType)
        //
        // },
      // 获取字典数据
      async getDictDataList() {
        await getDictList({}).then(res => {
          this.dictDataList = res.result
        })
      },
      // 筛选出通用单位的值
      formatUnit(dictCode) {
        for(const item of this.dictDataList) {
          if(item.dictCode === 'unit_common_' + dictCode) {
            return item.dictFullName
          }
        }
      },
      // 筛选出通用标题的值
      formatTitle(dictCode) {
        for(const item of this.dictDataList) {
          if(item.dictCode === 'line_title_common_' + dictCode) {
            return item.dictFullName
          }
        }
      },
      // 筛选出纵坐标名称的值
      formatName(dictCode) {
        for(const item of this.dictDataList) {
          if(item.dictCode === 'line_yaxis_common_' + dictCode) {
            return item.dictFullName
          }
        }
      },

        // 从数据字典获取数据
        async getDictDetails() {
            this.listLoading = true
            let param = {dictCode: "deviceType"}
            const deviceTypeRes = await getDictList(param)
            this.selectDeviceType = deviceTypeRes.result[0].children
            this.listLoading = false
        },
        async fetchData() {
            this.queryForm.curPage = this.pageInfo.curPage
            this.queryForm.pageSize = this.pageInfo.pageSize
            this.queryForm.parentId = 'bee561c7-f2db-45c7-8986-d84b1b75e2e6'
            this.listLoading = true
            const { result: { records, total }} = await getDataListByPage(this.queryForm)
            // console.log(records);
            this.list = records
            this.pageInfo.total = Number(total)
            this.listLoading = false
        },
        handleUnitConfig(row){
            this.$refs['unitConfig'].showDialog(row)
        },
        handleLineTitleConfig(row){
            this.$refs['lineTitleConfig'].showDialog(row)
        },
        handleLineYaxisConfig(row){
            this.$refs['lineYaxisConfig'].showDialog(row)
        },
    },
}
</script>
<style lang="scss" scoped>

</style>
