<template>
  <el-dialog
    :close-on-click-modal="false"
    append-to-body
    title="折线图纵坐标名称配置详情"
    :visible.sync="dialogDetailVisible"
    width="35%"
    top="25vh"
    center
    v-drag
  >
    <el-descriptions title="通用折线图纵坐标名称" class="margin-top" :column="3" border size="medium">
      <el-descriptions-item :labelStyle='labelStyle' :contentStyle='typeContentStyle'>
        <template slot="label">折线图纵坐标名称</template>
        <el-input v-model="common.dictFullName" style="width: 280px;margin-right: 10px;" />
        <el-button type="edit" @click="saveUnitCommon()">
          保存
        </el-button>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions title="个性化设置" class="margin-top title" :column="1" border size="medium">
      <template slot="extra">
        <el-button type="primary" size="small" @click="addVisible = true">添加</el-button>
      </template>
      <el-descriptions-item :labelStyle='labelStyle' v-for="item in list" :key="item" :label="item" v-if="item.dictCode.startsWith('line_yaxis_') && !item.dictCode.startsWith('line_yaxis_common_')">
        <template slot="label">{{item.dictName}}</template>
        <el-input v-model="item.dictFullName" style="width: 280px;margin-right: 10px;" />
        <el-button type="edit" @click="edit(item)">编辑</el-button>
        <el-button type="edit" @click="del(item)">删除</el-button>
      </el-descriptions-item>
    </el-descriptions>




    <el-form  :model="formData"  v-show="addVisible">

      <el-descriptions title="添加项目对应的折线图纵坐标名称" class="margin-top title" :column="2" border size="medium">
        <template slot="extra">
          <el-button type="primary" size="small" @click="addVisible = false">隐藏</el-button>
        </template>
      </el-descriptions>
      <el-form-item label="名称">
        <el-input v-model="formData.dictName" style="width: 280px"></el-input>
      </el-form-item>
      <el-form-item label="项目名称">
        <el-select v-model="formData.projectId" filterable size="medium">
          <el-option
            v-for="item in projectList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="折线图纵坐标名称">
      <el-input v-model="formData.dictFullName" style="width: 280px"></el-input>
    </el-form-item>
      <el-button type="edit" @click="save()">
        保存
      </el-button>
    </el-form>
  </el-dialog>
</template>

<script>

import {genUUID} from '@/utils/th_utils.js'
import { getDictList, saveDict, delDictById } from '@/api/system/dict-api'
import { getProjectList } from '@/api/project/project-api'

export default {
    data() {
        return {
            dialogDetailVisible: false,
            addVisible: false,
            parentData: {},
            formData: {},
            list: [],
            common: {},
            projectList: [],
            //内容样式
            contentStyle: {
                'text-align': 'center',
            },
            typeContentStyle: {
                'width': '160px'
            },
            //label样式
            labelStyle: { 'width': '100px' }
        }
    },
    created() {

    },
    methods: {
        showDialog(row) {
            this.parentData = { ...row }
            console.info(this.parentData)
            this.common = {}
            this.dialogDetailVisible=true
            this.addVisible=false
            this.fetchData()
            this.getProjectList()
        },
        async fetchData() {
            this.listLoading = true
            getDictList({parentDictCode:this.parentData.dictCode}).then(res => {
                this.list = res.result
                this.list.forEach(l => {
                    if(l.dictCode === 'line_yaxis_common_'+this.parentData.dictCode){
                        this.common = l
                    }
                })
            })
            this.listLoading = false
        },
        getProjectList() {
            getProjectList({}).then((res) => {
                this.projectList = res.result || []
            })
        },
        saveUnitCommon(){
            this.common.dictCode = 'line_yaxis_common_'+this.parentData.dictCode
            this.common.dictName = '通用折线图纵坐标名称'
            this.common.parentId = this.parentData.id
            if(!this.common.id){
                this.common.isAdd = true
                this.common.id = genUUID()
            }
            saveDict(this.common).then(() => {
                this.$baseMessage(
                   this.common.isAdd ? '新增成功！' : '修改成功!', 'success', 'vab-hey-message-success'
                )
                this.$emit('changeYaxisName')
                this.addVisible = false
            }).catch(() => {
                this.$baseMessage(
                    this.common.isAdd ? '新增失败！' : '修改失败!', 'error', 'vab-hey-message-error'
                )
            })
        },
        save() {
            this.formData.dictCode = 'line_yaxis_' +this.parentData.dictCode+'_'+ this.formData.projectId
            this.formData.id = genUUID()
            this.formData.isAdd = true
            this.formData.parentId = this.parentData.id
            saveDict(this.formData).then(() => {
                this.$baseMessage('新增成功！', 'success', 'vab-hey-message-success')
                this.fetchData()
            }).catch(() => {
                    this.$baseMessage('新增失败！', 'error', 'vab-hey-message-error')
                })
        },
        edit(row) {
            saveDict(row).then(() => {
                this.$baseMessage('修改成功！', 'success', 'vab-hey-message-success')
            }).catch(() => {
                this.$baseMessage('修改失败！', 'error', 'vab-hey-message-error')
            })
        },
        del(row) {
            if (row.id) {
                this.$baseConfirm('你确定要删除吗', null, async () => {
                    delDictById(row.id)
                        .then(() => {
                            this.fetchData()
                            this.$baseMessage('删除成功!', 'success', 'vab-hey-message-success'
                            )
                        })
                        .catch(() => {
                            this.$baseMessage('删除失败!', 'error', 'vab-hey-message-error')
                        })
                })
            }
        },
    }
}
</script>

<style scoped>
  .title {
    margin-top: 20px;
  }
</style>
