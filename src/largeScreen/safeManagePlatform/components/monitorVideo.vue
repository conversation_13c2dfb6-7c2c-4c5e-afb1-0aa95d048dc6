<template>
  <div class="page-box page-icon-t" style="pointer-events: auto;">
    <div class="title-box">
      <div class="title">
        <h2>视频监控</h2>
        <div class="dropdown-box">
          <span>监控地址:</span>
          <el-select
            v-model="videoId"
            style="width: 160px"
            :popper-append-to-body="false"
            @change="urlChange"
          >
            <el-option
              v-for="item in dataList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </div>
      </div>
    </div>
    <div class="contextBox">
      <!-- <video src="https://file.jxth.com.cn/group1/M00/00/2E/wKgKZ2O9JzyEIByhAAAAAMYRj8o482.mp4" autoplay loop style="width: 100%;height: 250px;"></video> -->
      <iframe
        allowfullscreen
        height="250"
        :src="videoUrl"
        width="100%"
        style="border: none;"
      />
    </div>
  </div>
</template>

<script>
  import { getDataList, token } from '@/api/scene/video-api'
  export default {
    props: {
      dataList: {
        type: Array,
        default() {
          return []
        }
      }
    },
    watch: {
      dataList: {
        handler(newVal) {
          if(newVal.length > 0) {
            this.videoId = newVal[0].id
            this.urlChange()
          }
        },
        immediate: true,
      },
    },
    data() {
      return {
        videoId: '',
        videoUrl: '',
      }
    },
    methods: {
      urlChange() {
        //this.videoUrl = this.realtimeUrl + this.token
        const selectedItem = this.dataList.find(item => item.id === this.videoId);
        if (selectedItem) {
          token({brand: selectedItem.brand}).then((res) => {
            this.videoUrl = selectedItem.realtimeUrl + res.result
          }).catch(() => {})
        }
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-box {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 10px;
    background: rgba(2,47,96,.68);
    .contextBox{
      padding: 10px;
      height: 270px;
      box-sizing: border-box;
    }
  }
  .dropdown-box {
    position: relative;
    z-index: 99;
    height: 32px;
    display: flex;
    align-items: center;
    padding-right: 10px;
    span {
      font-size: 16px;
      color: #fff;
      margin-right: 5px;
    }
    ::v-deep .el-input__inner {
      height: 24px;
      line-height: 24px;
      background: transparent;
      color: #00c2ff;
      font-size: 14px;
      border: 1px solid #00c2ff;
      box-sizing: border-box;
      user-select: none !important;
    }
    ::v-deep .el-input__icon {
      line-height: 24px;
      color: #00c2ff;
    }
  }
</style>
