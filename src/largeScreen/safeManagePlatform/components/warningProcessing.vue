<template>
  <div class="page-box page-icon-t"  style="pointer-events: auto;">
    <div class="title-box">
      <div class="title">
        <h2>预警处理</h2>
      </div>
    </div>

    <div class="contextBox">
      <vue-seamless-scroll v-if="tableData.length>0" :data="tableData" :class-option="optionSetting" class="seamless-warp">
      <div class="warnItem"  v-for="(item, index) in tableData" :key="index">
        <div class="triangle"></div>
        <div class="w_img"><img src="../assets/alert_icon.png" ><span>{{item.deviceName}}</span></div>
        <div class="w_cont">
          <!-- <div class="w_title">{{item.deviceName}}<span>采集时间:{{formateTime(item.prodTime)}}</span></div> -->
          <div class="w_info">
            <div class="w_item">采集时间：<span>{{formateTime(item.prodTime)}}</span></div>
            <div class="w_item">设备类型：<span>{{item.deviceTypeName}}</span></div>
            <div class="w_item">预警等级：<span>{{item.alarmLevel}}</span></div>
            <div class="processing">{{item.statusName}}</div>
          </div>
        </div>
      </div>

       </vue-seamless-scroll>
    </div>

  </div>
</template>

<script>
  import { parseTime } from '@/utils'
  import { getAlarmDataByPage } from '@/api/alarming/alarmData-api.js'
  export default {
    props: {
      projectIds: {
        type: Array,
        default() {
          return []
        }
      }
    },
    data() {
      return {
        tableData: []
      }
    },
    watch: {
      projectIds: {
        handler(newVal) {
          if(newVal.length > 0) {
            this.getDataList()
          }
        },
        immediate: true,
      },
    },
    computed: {
      optionSetting() {
        return {
          step: 0.6, // 速度，值越大，速度越快
          hoverStop: true, // 鼠标悬停效果，false为关闭该效果
          limitMoveNum: 2,
          singleHeight: 96,
          waitTime: 1000
        }
      }
    },
    methods: {
      async getDataList() {
        const queryForm = {
          curPage: 1,
          pageSize: 20,
          projectIds: this.projectIds,
        }
        const { result: { records }} = await getAlarmDataByPage(queryForm)
        this.tableData = records.map(item => {
          return {
            deviceName: item.deviceName,
            deviceTypeName: item.deviceTypeName,
            prodTime: item.prodTime,
            alarmLevel: item.alarmLevel,
            statusName: item.statusName,
          }
        })
      },
      formateTime(time) {
        const timeData =  parseTime(time)
        return timeData
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-box {
    width: 100%;
    height: 320px;
    box-sizing: border-box;
    background-color: rgba(2,47,96, .68);
    margin-bottom: 10px;
    .contextBox{
      padding: 10px;
      .warnItem{
        height: 90px;
        box-sizing: border-box;
        border: 1px solid rgba(106,173,228,.5);
        margin-bottom: 6px;
        display: flex;
        padding: 8px;
        justify-content: space-between;
        position: relative;
        .triangle{
          position: absolute;
          left: 10px;
          top: 0px;
          width: 0;
          height: 0;
          border-width: 10px;
          border-left: 0;
          border-style: solid;
          border-color: transparent transparent #4ea1fd;
          transform: rotate(90deg);
        }
        .w_img{
          text-align: center;
          margin-top: 10px;
          img{}
          span{
            display: block;
            background: rgba(226,15,22,0.25);
            color: #ff7d7d;
            font-size: 13px;
            padding: 4px 6px;
            border-radius: 4px;
            width: 90px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .w_cont{
          width: 326px;
          padding-left: 10px;
          clear: both;
          overflow: hidden;
          .w_title{
            color: #fff;
            font-size: 18px;
            width: 100%;
            line-height: 30px;
            position: relative;
            span{
              font-size: 13px;
              color: #90e1f7;
              display: inline-block;
              text-align: right;
              position: absolute;
              right: 0;
              bottom: 0;
            }
          }
          .w_info{
            position: relative;
            .w_item{
              color: #aecaf9;
              font-size: 14px;
              line-height: 24px;
              span{
                color: #fff;
              }
            }
            .processing{
              position: absolute;
              right: 0;
              bottom: 0;
              color: #fff;
              font-size: 13px;
              padding: 3px 8px;
              border-radius: 4px;
              background: rgba(37,255,124,0.4);
            }
            .noDone{
              background: rgba(255,69,69,0.6);
            }
          }
        }
      }
    }
    .seamless-warp {
      overflow: hidden;
      width: 100%;
      height: 250px;
    }

  }
</style>
