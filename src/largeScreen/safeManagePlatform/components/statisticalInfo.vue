<template>
  <div class="page-box page-icon-t">
    <div class="title-box">
      <div class="title">
        <h2>统计信息</h2>
      </div>
    </div>
    <div class="contextBox">
      <ul>
      	<li>项目总数：<span>{{dataInfo.totalProjects}}个</span></li>
        <li>设备总数：<span>{{dataInfo.totalDevice}}个</span></li>
        <li>设备离线/在线：<span>{{dataInfo.totalDevice - dataInfo.onlineDevice}} / {{dataInfo.onlineDevice}}</span></li>
<!--        <li>累计监测数量：<span>{{dataInfo.alarmNum}}条</span></li>-->
        <li>预警数量：<span>{{dataInfo.alarmNum}}条</span></li>
        <!-- <li>称重系统过车数量：<span>{{dataInfo.carWeightNum}}辆</span></li> -->
        <li>视频数量：<span>{{dataInfo.videoConfigNum}}个</span></li>
      </ul>
    </div>
  </div>
</template>

<script>
  export default {
    props: {
      dataInfo: {
        type: Object,
        default() {
          return {}
        }
      }
    },
    data() {
      return {

      }
    },
    created() {
    },
    methods: {
    }
  }
</script>

<style lang="scss" scoped>
  .page-box {
    width: 100%;
    // height: 380px;
    box-sizing: border-box;
    background: rgba(2,47,96,.68);
    margin-bottom: 10px;
    .contextBox{
      padding: 10px;
      ul{
        margin: 0;
        padding: 0;
        li{
          list-style: none;
          line-height: 38px;
          color: #aecaf9;
          font-size: 18px;
          letter-spacing: 2px;
          border-bottom: 1px solid rgba(5,78,143,.5);
          span{
            color: #fff;
            font-size: 20px;
          }
        }
      }
    }

  }
</style>
