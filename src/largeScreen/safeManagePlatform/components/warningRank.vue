<template>
  <div class="page-box page-icon-t" style="pointer-events: auto;">
    <div class="title-box">
      <div class="title">
        <h2>预警排行</h2>
      </div>
    </div>
    <div class="contextBox">
      <div
        v-loading="loading"
        class="tablebox"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.3)">

        <div style="width: 100%;height: 100%;" class="chart">
          <div class="active" v-if="caseData.length > 0 && caseData[0].alarmCount > 0">
            <div class="rank"><span>1</span>{{ caseData[0].projectName }}</div>
            <div class="lineBar"><div class="bar" /><span>{{ caseData[0].alarmCount }}</span></div>
          </div>
          <div v-if="caseData.length > 1 && caseData[1].alarmCount > 0">
            <div class="rank"><span>2</span>{{ caseData[1].projectName }}</div>
            <div class="lineBar"><div class="bar" :style="{'width':(caseData[1].alarmCount/caseData[0].alarmCount*80)+'%'}" /><span>{{ caseData[1].alarmCount }}</span></div>
          </div>
          <div v-if="caseData.length > 2 && caseData[2].alarmCount > 0">
            <div class="rank"><span>3</span>{{ caseData[2].projectName }}</div>
            <div class="lineBar"><div class="bar" :style="{'width':(caseData[2].alarmCount/caseData[0].alarmCount*60)+'%'}" /><span>{{ caseData[2].alarmCount }}</span></div>
          </div>
        </div>

      </div>

    </div>
  </div>
</template>


<script>
  import { parseTime } from '@/utils'
  export default {
    props: {
      caseData: {
        type: Array,
        default() {
          return []
        }
      }
    },
    data() {
      return {
        date: 'MONTH',
        loading: false,
        startTime: '',
        endTime: ''
      }
    },
    methods: {
      getFormateDte() {
        const currentDate = parseTime(new Date(), '{y}-{m}-{d}')
        const currentYear = currentDate.substring(0, 4)
        const currentMonth = currentDate.substring(5, 7)
        if(this.date === 'DAY') {
          const currentTime = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s}')
          this.startTime = currentDate + ' 00:00:00'
          this.endTime = currentTime
        } else if(this.date === 'MONTH') {
          this.startTime = `${currentYear}-${currentMonth}-01`
          this.endTime = currentDate + ' 23:59:59'
        } else if(this.date === 'YEAR') {
          this.startTime = `${currentYear}-01-01`
          this.endTime = currentDate + ' 23:59:59'
        }
      }
    }
  }
</script>

<style lang="scss" scoped>
  .page-box {
    position: relative;
    z-index: 99;
    width: 100%;
    height: 280px;
    box-sizing: border-box;
    background-color: rgba(2,47,96, .58);
  }
  .dropdown-box {
    height: 32px;
    display: flex;
    align-items: center;
    padding-right: 10px;
    ::v-deep .el-input__inner {
      height: 24px;
      line-height: 24px;
      background: transparent;
      color: #00c2ff;
      font-size: 14px;
      border: 1px solid #00c2ff;
      box-sizing: border-box;
    }
    ::v-deep .el-input__icon {
      line-height: 24px;
      color: #00c2ff;
    }
  }
  .contextBox{
    padding: 0px;
    width: 100%;
    height: 235px;
    margin-top: 10px;
    .chart{
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      >div{
        padding: 10px;
        margin-left: 25px;
        .rank{
          margin-bottom: 5px;
          font-weight: bolder;
          font-size: 16px;
          span{
            color: #fff;
            display: inline-block;
            width: 30px;
            height: 30px;
            text-align: center;
            vertical-align: middle;
            box-sizing: border-box;
            padding-top: 5px;
            margin-right: 5px;
          }
        }
        .lineBar{
          .bar{
            display: inline-block;
            width: 80%;
            height: 12px;
            border-radius: 12px;
            vertical-align: middle;
            margin-right: 10px;
          }
          span{
            font-size: 18px;
            font-weight: bold;
          }
        }
        &:nth-of-type(1){
          .rank{
            span{
              background: url('../assets/icon7.png');
              background-size: cover;
            }
            color: #fd5353;
          }
          .lineBar{
            .bar{
              background: linear-gradient(to right,  rgba(238,78,78,1), rgb(253, 125, 34));;
            }
            color: #fd5353;
          }
        }
        &:nth-of-type(2){
          .rank{
            span{
              background: url('../assets/icon8.png');
              background-size: cover;
            }
            color: #ffb726;
          }
          .lineBar{
            .bar{
              background: linear-gradient(to right, #ff942b , #ffce2b);;
            }
            color: #ffb726;
          }
        }
        &:nth-of-type(3){
          .rank{
            span{
              background: url('../assets/icon9.png');
              background-size: cover;
            }
            color: #00b8f8;
          }
          .lineBar{
            .bar{
              background: linear-gradient(to right, rgba(34,253,227,1) , rgba(14,127,221,1));;
            }
            color: #00b8f8;
          }
        }
      }
    }
  }
</style>
