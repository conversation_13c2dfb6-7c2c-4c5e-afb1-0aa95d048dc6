<template>
  <div class="page-box page-icon-t" style="pointer-events: auto;">
    <div class="title-box">
      <div class="title">
        <h2>项目信息</h2>
        <!-- <div class="dropDown"></div> -->
      </div>
    </div>
    <div class="contextBox">
      <table class="my-table" border="0" cellspacing="0">
        <thead>
          <tr>
            <th width="140">项目名称</th>
            <th width="90">项目类型</th>
            <th width="90">安装时间</th>
            <th width="100">预警等级</th>
          </tr>
        </thead>
      </table>
      <vue-seamless-scroll v-if="tableData.length>0" :data="tableData" :class-option="optionSetting"
        class="seamless-warp">
        <table class="my-table" border="0" cellspacing="0">
          <tbody>
            <tr v-for="(item,index) in tableData" :key="index">
              <td width="140">{{item.name}}</td>
              <td width="90">{{item.type}}</td>
              <td width="90">{{item.startTime ? dateFormat(item.startTime) : '-'}}</td>
              <td width="100" v-if="item.alarmLevel === '1'">红色预警</td>
              <td width="100" v-if="item.alarmLevel === '2'">橙色预警</td>
              <td width="100" v-if="item.alarmLevel === '3'">黄色预警</td>
              <td width="100" v-if="item.alarmLevel === '4'">蓝色预警</td>
              <td width="100" v-if="item.alarmLevel === '0'">正常</td>
            </tr>
          </tbody>
        </table>
      </vue-seamless-scroll>

    </div>
  </div>
</template>

<script>
  import { parseTime } from "@/utils";
  export default {
    props: {
      tableData: {
        type: Array,
        default() {
          return []
        }
      }
    },
    data() {
      return {
        projectTypeList: [],
      }
    },
    computed: {
      optionSetting() {
        return {
          step: 0.5, // 速度，值越大，速度越快
          hoverStop: true, // 鼠标悬停效果，false为关闭该效果
          limitMoveNum: 6,
          singleHeight: 32,
          waitTime: 1000
        }
      }
    },
    methods: {
      dateFormat(date) {
        if (!date) {
          return ''
        }
        return parseTime(new Date(date), '{y}-{m}-{d}')
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-box {
    width: 100%;
    // height: 380px;
    box-sizing: border-box;
    background: rgba(2,47,96,.68);
    margin-bottom: 10px;
    .contextBox{
      padding: 10px 0;
      height: 280px;
      overflow: hidden;
      .my-table {
        width: 100%;
        text-align: center;
        table-layout:fixed;
        thead {
          background-color: #084891;
          margin-bottom: 5px;
        }
      }
      .my-table th {
        color: #23cefd;
        font-size: 16px;
        line-height: 32px;
        font-weight: normal;
      }
      .my-table>tbody>tr>td {
        font-size: 13px;
        color: #9fccff;
        line-height: 32px;
        box-sizing: border-box;
        padding: 0 5px;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      table.my-table tr:nth-child(even) {
        background-color: rgba(8,72,145,0.5);
      }
      .seamless-warp {
        overflow: hidden;
        width: 100%;
        height: 200px;
      }
    }
  }
</style>
