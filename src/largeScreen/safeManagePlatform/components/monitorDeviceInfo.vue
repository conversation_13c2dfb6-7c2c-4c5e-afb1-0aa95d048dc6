<template>
  <div class="page-box page-icon-t" style="pointer-events: auto;">
    <div class="title-box">
      <div class="title">
        <h2>监控设备信息</h2>
        <!-- <div class="dropDown"></div> -->
      </div>
    </div>
    <div class="contextBox">
      <table class="my-table" border="0" cellspacing="0">
        <thead>
          <tr>
            <th width="150">项目名称</th>
            <th width="100">设备名称</th>
            <th width="100">类型</th>
            <th width="100">设备状态</th>
          </tr>
        </thead>
      </table>
      <vue-seamless-scroll v-if="dataList.length>0" :data="dataList" :class-option="optionSetting"
        class="seamless-warp">
        <table class="my-table" border="0" cellspacing="0">
          <tbody>
            <tr v-for="(item,index) in dataList" :key="index">
              <td width="150">{{item.projectName}}</td>
              <td width="100">{{item.deviceName}}</td>
              <td width="100">{{item.deviceTypeName}}</td>
              <td width="100">
                <span :class="['status', item.status === 1 ? 'offline' : '']">{{item.status === 1 ? '离线' : '在线'}}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </vue-seamless-scroll>

    </div>
  </div>
</template>

<script>
  import { getInterruptEmailList } from '../api/safeManage-api'
  export default {
    props: {
      projectIds: {
        type: Array,
        default() {
          return []
        }
      }
    },
    data() {
      return {
        dataList: [],
      }
    },
    computed: {
      optionSetting() {
        return {
          step: 0.6, // 速度，值越大，速度越快
          hoverStop: true,// 鼠标悬停效果，false为关闭该效果
          limitMoveNum: 6,
          singleHeight: 32,
          waitTime: 1000
        }
      }
    },
    watch: {
      projectIds: {
        handler(newVal) {
          if(newVal.length > 0) {
            this.getInterruptEmailList()
          }
        },
        immediate: true,
      },
    },
    methods: {
      async getInterruptEmailList() {
        const { result } = await getInterruptEmailList({projectIds: this.projectIds})
        this.dataList = result.map(item => {
          return {
            projectName: item.projectName,
            deviceName: item.deviceName,
            deviceTypeName: item.deviceTypeName,
            status: item.status
          }
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  .page-box {
    width: 100%;
    // height: 380px;
    box-sizing: border-box;
    background: rgba(2,47,96, .68);
    margin-bottom: 10px;
    .contextBox{
      padding: 10px 0;
      height: 280px;
      overflow: hidden;
      .my-table {
        width: 100%;
        text-align: center;
        thead {
          background-color: #084891;
          margin-bottom: 5px;
        }
      }
      .my-table th {
        color: #23cefd;
        font-size: 16px;
        line-height: 32px;
        font-weight: normal;
      }
      .my-table>tbody>tr>td {
        font-size: 13px;
        color: #9fccff;
        line-height: 32px;
        box-sizing: border-box;
        padding: 0 5px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      table.my-table tr:nth-child(even) {
        background-color: rgba(8,72,145,0.5);
      }
      .seamless-warp {
        overflow: hidden;
        width: 100%;
        height: 220px;
      }
      .status {
        position: relative;
        &:before {
          content: '';
          position: absolute;
          top: 50%;
          margin-top: -3px;
          left: -12px;
          width: 6px;
          height: 6px;
          border-radius: 50%;
          background-color: #68ff3a;
        }
        &.offline {
          &:before {
            background-color: #ff0000;
          }
        }
      }
    }
  }
</style>
