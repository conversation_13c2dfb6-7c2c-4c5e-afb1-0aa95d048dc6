
.boxall{ border: 1px solid rgba(25,186,139,.17); padding:0 25px 25px 25px;  background: rgba(255,255,255,.04) url(../assets/line.png); position: relative; margin-bottom: 15px; z-index: 10;}
.boxall:before,.boxall:after{ position:absolute; width: 10px; height: 10px; content: "";  border-top: 2px solid #02a6b5; top: 0;}
.boxall:before,.boxfoot:before{border-left: 2px solid #02a6b5;left: 0;}
.boxall:after,.boxfoot:after{border-right: 2px solid #02a6b5; right: 0;}
.alltitle{ font-family: "微软雅黑"; font-size:20px; color:#fff; font-weight: bolder; text-align: center; line-height: 50px;position: relative;border-bottom: 1px solid rgba(255,255,255,.2);}
.boxfoot{ position:absolute; bottom: 0; width: 100%; left: 0;}
.boxfoot:before,.boxfoot:after{ position:absolute; width: 10px; height: 10px;  content: "";border-bottom: 2px solid #02a6b5; bottom: 0;}

.noneData{width: 100%;height: 100%; background: url(../assets/nonedata.png);background-repeat: no-repeat;background-position: center center;background-size: auto 60%;}

.boxall1{
    background: url(../assets/boxBg.png);
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    padding-bottom: 10px;
}
.boxall1 .alltitle{
    border-bottom: none;
    border-bottom: none;
    text-align: left;
    padding-left: 40px;
}
.boxall1 .boxfoot{display: none;}
.newBox {
    padding: 15px;
    border: none;
    background: rgba(24,67,121,0.5);
    margin: 0px;
    width: 100%;
    height: 100%;
}
.newBox .alltitle{
    background: url(../assets/itemTitle.png);
    background-size: auto 100%;
    background-repeat: no-repeat;
    background-position: left top;
    text-align: left;
    padding-left: 20px;
    height: 55px;
    border: none;
    line-height: 40px;
    font-size: 18px;
    font-style: italic;
}
.newBox.boxall:before, .newBox.boxall:after{display: none;}
.newBox .boxfoot:before,.newBox .boxfoot:after{display: none;}

.smBox{
  padding: 15px;
  border: none;
  margin: 0px;
  width: 100%;
  height: 100%;
  background: rgba(0,29,61,0.6);
  box-shadow: rgba(0,29,61,0.16) 0px 0px 35px 5px inset;
}
.smBox .alltitle{
  background: url(../assets/itemTitle.png);
  background-size: auto 100%;
  background-repeat: no-repeat;
  background-position: left top;
  text-align: left;
  padding-left: 20px;
  height: 55px;
  border: none;
  line-height: 40px;
  font-size: 18px;
  font-style: italic;
}
.smBox.boxall:before, .smBox.boxall:after{width: 15px; height: 15px;}
.smBox .boxfoot:before,.smBox .boxfoot:after{width: 15px; height: 15px;}
  .title-box {
    width: 100%;
    height: 32px;
    background-image: url('../assets/title_bg.png');
    background-repeat: no-repeat;
    box-sizing: border-box;
    position: relative;
  }
   .title-box::after{
    content: "";
    position: absolute;
    bottom: -14px;
    left: -16%;
    width: 100%;
    height: 32px;
    z-index: 0;
    background: url('../assets/title_light.png') no-repeat bottom left;
  }
   .title-box  .title{
     display: flex;
     justify-content: space-between;
   }
    .title-box  .title h2 {
      z-index: 1;
      position: relative;
      line-height: 32px;
      margin: 0 0 0 16px;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 3px;
      font-family: Microsoft YaHei;
      color: #fff;
      font-style: italic;
    }
