<template>
  <div style="width: 100%;height: 100%;">
    <!-- <div class="mapType">
      <span :class="{'now':mapType==1}" @click="changeType(1)">地图</span>
      <span :class="{'now':mapType==2}" @click="changeType(2)">混合</span>
    </div> -->

    <baidu-map class="map" ak="dVcHfWNKUGr553lknRuF17dQlM4HGr7I" :scroll-wheel-zoom="true" :center="center" :zoom="zoom"
      @ready="handler" @zoomend="syncCenterAndZoom" :min-zoom="7">

     <bm-marker v-for="(item,i) in projectList" :key="item.id"
        :position="{lng: item.longitude, lat: item.latitude}" :icon="{url:IconUrl[i%2], size: {width: 152, height: 94}}"
        @click="showDetail(item)">
        <bm-label v-if="zoom >= 9" :content="writeDiv(item,i%2)" :offset="{width:-45,height:-100}"
          :position="{lng: item.longitude, lat: item.latitude}" :label-style="labelStyle"  @click="showDetail(item)" />
        <bm-label v-else :content="writeDivLabel(item)" :offset="{width:-15,height:-30}"
          :position="{lng: item.longitude, lat: item.latitude}" :label-style="labelStyle"  @click="showDetail(item)" />
      </bm-marker>

      <!-- <bm-label v-else v-for="(item,i) in sameProjects" :key="item.id" :content="writeLabel(item)"
        :position="{lng: item[0].longitude, lat: item[0].latitude}" :label-style="labelStyle"
        @click="showDetail(item)" /> -->
      <bm-boundary name="江西省" :stroke-weight="4" stroke-color="blue" fill-color="black" :fill-opacity="0.01" />
    </baidu-map>
    <!-- 项目详情 -->
    <el-dialog
      title="项目详情"
      :visible.sync="dialogDetails"
      :close-on-click-modal="true"
      width="700px"
      @close="closeDialog"
    >
      <div class="detail-box">
        <div class="detail-img">
          <el-image :src="projectInfo.pic"></el-image>
        </div>
        <el-descriptions :column="1" border size="medium">
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{projectInfo.name}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目类型</template>
            {{projectInfo.type}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">安装时间</template>
            {{projectInfo.startTime}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目地址</template>
            {{projectInfo.address}}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="link-btn">
        <el-link type="primary" @click="toOnlineMonitor">在线监控</el-link>
        <el-link type="primary">/</el-link>
        <el-link type="primary" @click="toProjectInfo">项目信息</el-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="dialogDetails = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { BmlCurveLine } from 'vue-baidu-map'
  import BaiduMap from 'vue-baidu-map/components/map/Map.vue'
  import BmMarker from 'vue-baidu-map/components/overlays/Marker' // 点标注
  import BmLabel from 'vue-baidu-map/components/overlays/Label.vue'
  import BmBoundary from 'vue-baidu-map/components/others/Boundary.vue'
  import { dateFormat } from '@/utils/th_utils'
  export default {
    components: {
      BaiduMap,
      BmMarker,
      BmLabel,
      BmBoundary,
    },
    props: {
      projectList: {
        type: Array,
        default() {
          return []
        }
      },
    },
    watch: {
      'projectList': {
        handler(newVal) {
          if(newVal.length > 0) {
            newVal.some(item => {
              if(item.latitude && item.longitude) {
                this.center ={
                  lat: item.latitude,
                  lng: item.longitude
                }
                return true
              }
            })
          }
        },
        immediate: true
      }
    },
    data() {
      return {
        dialogDetails: false,
        projectInfo: {},
        center: { // 经纬度
          lat: 27.198635,
          lng: 115.907261
        },
        zoom: 8.5, // 地图展示级别
        siteStartX: [], // 经纬度
        labelStyle: {
          backgroundColor: 'transparent',
          border: 'none',
          padding: '0px ',
          cursor: 'pointer',
          display: 'block'
        },
        IconUrl: [
          'http://106.227.83.37:9001/files/bridgeMonitoring/902aa88d-190a-43f0-a011-2bfaa3e11764.jpg',
          'http://106.227.83.37:9001/files/bridgeMonitoring/5a773369-fde9-4472-a339-c19db668ec42.png'
        ],
        mapType: 1,
        map: null,
      }
    },
    mounted() {
    },
    beforeDestroy() {
      this.map = null
    },
    methods: {
      writeDiv(item, i) {
        return "<div class='mapWindow " + (i === 1 ? 'mapWindow1' : 'mapWindow') + "' ><div class='title'>" + item
          .name +
          "<span class='txt-over'>" + item.name +"</span>" +
          "</div><div class='content'>" +
          '<p><span>地址: </span>' + item.address + '</p>' +
          '<p><span>安装人员: </span>' + item.manager + '</p>' +
          '<p><span>项目类型: </span>' + item.type + '</p>' +
          '<p><span>安装时间: </span>' + item.startTime + '</p>' +
          '</div></div>'
      },
      writeDivLabel(item) {
        return "<div class='labelWindowDiv'>" + item.name + "</div>"
      },
      writeLabel(item) {
        return "<div class='labelWindow'>" + item[0].projectName + '<br/>' + item.length + '个</div>'
      },
      changeType(n) {
        if(!this.map) return
        this.mapType = n
        if (n == 1) {
          this.map.setMapType(BMAP_NORMAL_MAP)
        } else {
          this.map.setMapType(BMAP_HYBRID_MAP)
        }
      },
      dateFormat(row, column, cellValue, index) {
        var date = row[column.property]
        if (!date) {
          return ''
        }
        return dateFormat(new Date(date), null)
      },
      handler({BMap,map}) {
        // 获取百度地图对象
        this.$nextTick(() => {
          this.map = map
          // this.map.setMapStyleV2({
          //   // styleId: '9595b1c970298be4075a5f6c42f55dbd'
          //   styleId: '8a8793598c07f9a1ad9787d44603d096'
          // })
          this.map.setMapStyle({ style: 'midnight' })
          map.setMapType(BMAP_HYBRID_MAP)
          // var view = map.getViewport(eval(this.siteStartX))
          // this.zoom = view.zoom
          // this.center = view.center
        })
      },
      syncCenterAndZoom(e) {
        this.zoom = e.target.getZoom()
      },
      showDetail(item) {
        this.projectInfo = {...item}
        this.dialogDetails = true
      },
      closeDialog() {
        this.projectInfo = {}
      },
      toOnlineMonitor() {
        this.dialogDetails = false
        this.$store.commit('acl/setProjectId', this.projectInfo.id)
        this.$baseEventBus.$emit('reload-router-view')
        this.$router.replace("/index")
      },
      toProjectInfo() {

      },
    }
  }
</script>

<style lang="scss" scoped>
  .map {
    width: 100%;
    height: 100%;
  }

  .mapType {
    height: 33px;
    position: absolute;
    z-index: 4;
    top: 140px;
    left: 500px;
    background-color: rgba($color: #002863, $alpha: 0.5);
    box-shadow: 0 0 10px #0f8fe0;
    color: #ffffff;
    border-radius: 16px;
    font-weight: normal;
    font-size: 18px;
    line-height: 33px;

    span {
      padding: 0px 20px;
      display: inline-block;
      cursor: pointer;
    }

    .now {
      box-shadow: 0px 0px 10px #2bb6fb inset;
      background-color: rgba($color: #2eb1ff, $alpha: 0.75);
      color: #ffffff;
      border-radius: 16px;
    }
  }
  ::v-deep .detail-box {
    display: flex;
    align-items: center;
    .detail-img {
      width: 250px;
      margin-right: 20px;
      img {
        width: 100%;
      }
    }
    .el-descriptions-item__label {
      width: 100px;
    }
    .el-descriptions-item__content {
      width: 250px;
    }
  }
  .link-btn {
    margin-top: 20px;
  }
</style>
<style lang="scss">
  .labelWindow {
    width: 120px;
    height: 120px;
    text-align: center;
    border-radius: 50%;
    color: #ffffff;
    font-size: 18px;
    font-weight: bolder;
    background-image: linear-gradient(125deg, #e4ffcd, #029fff, #0166ff, #e4ffcd);
    background-size: 400%;
    animation: bganimation 15s infinite;
    display: flex;
    justify-content: center;
    align-items: center;
    box-shadow: 5px 5px 10px rgba(0, 102, 204, 0.2);

    @keyframes bganimation {
      0% {
        background-position: 0% 50%;
      }

      50% {
        background-position: 100% 50%;
      }

      100% {
        background-position: 0% 50%;
      }
    }
  }
  .labelWindowDiv {
    width: 180px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #ffffff;
    font-size: 14px;
    font-weight: bolder;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    background: url(../assets/title_bg.png);
    background-size: 100% 100%;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .mapWindow {
    width: 240px;
    background: url(../assets/mapTitle2.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    padding: 10px;

    .title {
      position: relative;
      width: 100%;
      height: 30px;
      box-sizing: border-box;
      padding: 0 10px;
      line-height: 30px;
      color: #fff;
      font-weight: bold;
      font-style: italic;
      font-size: 20px;
      letter-spacing: 2px;
      text-align: center;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;

      .txt-over  {
        display: none;
      }
      &:hover {
        text-overflow: inherit;
        overflow: inherit;
        .txt-over {
          // display: block;
          position: absolute;
          top: -30px;
          left: 50%;
          transform: translateX(-50%);
          padding: 5px 10px;
          border: 1px solid #232323;
          background-color: #fff;
          font-size: 12px;
          color: #232323;
          line-height: 1;
          font-style: normal;
        }
      }
    }

    &.mapWindow1 {
      background: url(../assets/mapTitle1.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }

    .content {
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0px 0px 16px 16px;
      margin: 4px 2px 3px;
      color: #fff;
      padding: 10px;

      p {
        line-height: 100%;
        margin: 0px;
        font-size: 16px;
        margin-bottom: 10px;
        white-space: normal;

        span {
          color: #afcadc;
        }
      }

    }
  }
</style>
