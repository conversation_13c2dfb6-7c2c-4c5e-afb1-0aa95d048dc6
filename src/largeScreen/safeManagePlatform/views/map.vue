<template>
  <div style="width: 100%;height: 100%;">
    <div id="mapTBox" style="width: 100%; height: 100%;"></div>

    <!-- 项目详情 -->
    <el-dialog
      title="项目详情"
      :visible.sync="dialogDetails"
      :close-on-click-modal="true"
      width="700px"
      @close="closeDialog"
    >
      <div class="detail-box">
        <div class="detail-img">
          <el-image :src="projectInfo.pic"></el-image>
        </div>
        <el-descriptions :column="1" border size="medium">
          <el-descriptions-item>
            <template slot="label">项目名称</template>
            {{projectInfo.name}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目类型</template>
            {{projectInfo.type}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">安装时间</template>
            {{projectInfo.startTime}}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">项目地址</template>
            {{projectInfo.address}}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="link-btn">
        <el-link type="primary" @click="toOnlineMonitor">在线监控</el-link>
        <el-link type="primary">/</el-link>
        <el-link type="primary" @click="toProjectInfo">项目信息</el-link>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="danger" @click="dialogDetails = false">
          关闭
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import gcoord from 'gcoord';
import jxGeoJson from '@/assets/jx_geojson.js'

export default {
  props: {
    projectList: {
      type: Array,
      default() {
        return []
      }
    },
  },
  watch: {
    'projectList': {
      handler(newVal) {
        if (newVal.length > 0 && this.map) {
          this.createrMarker()
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      dialogDetails: false,
      projectInfo: {},
      map: null,
    }
  },
  mounted() {
    this.createMap()
  },
  beforeDestroy() {
    if (this.map) {
      this.map.clearOverLays()
      this.map = null
    }
  },
  methods: {
    createMap() {
      let mapPoint = null
      if (this.projectList.length > 0) {
        const project = this.projectList[0]
        mapPoint = new T.LngLat(project.longitude, project.latitude)
      }

      this.map = new T.Map('mapTBox', {
        projection: 'EPSG:4326',
        minZoom: 4,
      })
      // 使用江西省中心坐标 (115.892151, 28.676493) 作为默认中心
      this.map.centerAndZoom(mapPoint || new T.LngLat(115.892151, 28.676493), 7);

      var ctrl = new T.Control.MapType();
      this.map.addControl(ctrl);
      this.map.setMapType(TMAP_HYBRID_MAP)

      // 添加江西省边界
      let points = [];
      const originPoints = jxGeoJson.features[0].geometry.coordinates[0][0]
      const len = originPoints.length
      for(let i = 0; i < len; i++) {
        points.push(new T.LngLat(originPoints[i][0], originPoints[i][1]))
      }
      const polygon = new T.Polygon(points,{
        color: "blue",
        weight: 3,
        opacity: 0.5,
        fillOpacity: 0.1,
      });
      this.map.addOverLay(polygon);

      this.createrMarker()
    },

    createrMarker() {
      let _this = this
      const projectList = this.projectList
      if (!projectList.length) return

      let definedOverlay = T.Overlay.extend({
        initialize: function(lnglat, projectName, projectData, options) {
          this.lnglat = lnglat
          this._projectName = projectName
          this._projectData = projectData
          this.setOptions(options)
        },
        onAdd: function(map) {
          let div = this._div = document.createElement("div");
          div.style.position = "absolute"
          div.style.zIndex = 99999
          div.style.width = '206px'
          div.style.height = '90px'
          div.style.transform = 'translate(-103px, -45px)'

          let title = document.createElement('div')
          title.style.width = '100%'
          title.style.height = '43px'
          title.style.lineHeight = '43px'
          title.style.boxSizing = 'border-box'
          title.style.padding = '0 30px'
          title.style.fontSize = '14px'
          title.style.color = '#fff'
          title.style.cursor = 'pointer'
          title.style.textAlign = 'center'
          title.style.whiteSpace = 'nowrap'
          title.style.textOverflow = 'ellipsis'
          title.style.overflow = 'hidden'
          title.style.backgroundImage = 'url(https://minio.jxth.com.cn/files/bridgeMonitoring/ed3712f8-9e75-4ddc-ad92-378fd571de93.png)'
          title.style.backgroundSize = '100% 100%'
          div.appendChild(title);
          title.appendChild(document.createTextNode(this._projectName));

          const icon = document.createElement('div')
          icon.style.width = '53px'
          icon.style.height = '47px'
          icon.style.margin = '0 auto'
          icon.style.backgroundImage = 'url(https://minio.jxth.com.cn/files/bridgeMonitoring/a37f5fa5-e029-4667-82e3-db9a265ce7c7.png)'
          icon.style.backgroundSize = '100% 100%'
          div.appendChild(icon);

          this._div.addEventListener("click", () => {
            _this.showDetail(this._projectData)
          })
          map.getPanes().overlayPane.appendChild(this._div);
          this.update(this.lnglat);
        },
        onRemove: function() {
          const parent = this._div.parentNode;
          if (parent) {
            parent.removeChild(this._div);
            this._div = null;
          }
        },
        update: function() {
          var pos = _this.map.lngLatToLayerPoint(this.lnglat);
          this._div.style.top = (pos.y - 36) + "px";
          this._div.style.left = (pos.x - 11) + "px";
        },
      })

      projectList.forEach((item, index) => {
        let point = new T.LngLat(item.longitude, item.latitude)
        let pdefinedOverlay = new definedOverlay(point, item.name, item, {});
        this.map.addOverLay(pdefinedOverlay);
      })
    },

    showDetail(item) {
      this.projectInfo = {...item}
      this.dialogDetails = true
    },

    closeDialog() {
      this.projectInfo = {}
    },

    toOnlineMonitor() {
      this.dialogDetails = false
      this.$store.commit('acl/setProjectId', this.projectInfo.id)
      this.$baseEventBus.$emit('reload-router-view')
      this.$router.replace("/index")
    },

    toProjectInfo() {
      // 实现项目信息跳转逻辑
    },
  }
}
</script>

<style lang="scss" scoped>
#mapTBox {
  position: relative;
  width: 100%;
  height: 100%;
}

.detail-box {
  display: flex;
  gap: 20px;

  .detail-img {
    flex-shrink: 0;
    width: 200px;
    height: 150px;

    .el-image {
      width: 100%;
      height: 100%;
      border-radius: 4px;
    }
  }
}

.link-btn {
  margin-top: 20px;
  text-align: center;

  .el-link + .el-link {
    margin-left: 10px;
  }
}
</style>

<style>
.tdt-control-copyright {
  display: none;
}
</style>
