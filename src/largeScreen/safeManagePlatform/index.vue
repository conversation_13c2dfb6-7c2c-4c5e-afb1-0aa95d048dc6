<template>
  <div ref="bigScreen" v-loading="loading" class="wrapper" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.3)">
    <div class="container">
      <projectMap style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1; pointer-events: auto;" :projectList="projectList" />
      <div class="mask" style="pointer-events: none; z-index: 2;"></div>
      <div class="dashboard-container" style="width: 1920px;height: 1080px;pointer-events: none;position: absolute; z-index: 3;" :style="styles">
        <div class="header" style="z-index:4;pointer-events:auto;">
          <div class="title-date" style="z-index:5">
            <div class="time-text">{{dateTime}}</div>
            <div>
              <span class="date-text">{{dateYear}}</span>
              <span class="week-text">星期{{dateWeek}}</span>
            </div>
          </div>
          <!-- <div class="weather" style="z-index:5">
            <img v-if="weatherList.length !== 0" :src="require('../assets/largeScreen/weather/'+weatherList[0].code_day+'@1x.png')">
            <div v-if="weatherList.length !== 0">
              <span class="temp-text">{{weatherList[0].low}}-{{weatherList[0].high}}℃</span>
              <span class="weather-text">{{weatherList[0].text_day}}</span>
            </div>
          </div> -->
          <i style="z-index:5" class="el-icon-switch-button close-btn" @click="logout" />
        </div>
        <div class="content-box">
          <div class="left-area">
            <statisticalInfo :dataInfo="statisticalInfoData" />
            <projectInformation :tableData="projectList" />
            <warningProcessing :projectIds="allProjectIds" />

          </div>

          <div class="right-area">
            <monitorDeviceInfo :projectIds="allProjectIds" />
            <monitorVideo :data-list="videoList" />
            <warningRank :caseData="warningRankList" />
          </div>
        </div>
        <!-- <div class="footer-box">
          <div class="footer-content">
            <span>全国项目分布区域</span>
          </div>
          <div class="project-num">
            <div class="project-item" v-for="(item, index) in projectNumInfo" :key="index" style="pointer-events: auto;">
              <div class="province">{{item[0].provinceName}}</div>
              <div class="num">{{item.length}}</div>
            </div>
          </div>
        </div> -->
      </div>
    </div>

  </div>
</template>

<script>
  import { mapGetters } from "vuex";
  import { getComputedStyle, debounce, parseTime } from '@/utils/th_utils'
  import screenfull from 'screenfull'
  import projectMap from './views/map'
  import statisticalInfo from './components/statisticalInfo.vue'
  import projectInformation from './components/projectInformation.vue'
  import warningProcessing from './components/warningProcessing.vue'
  import monitorDeviceInfo from './components/monitorDeviceInfo.vue'
  import monitorVideo from './components/monitorVideo.vue'
  import warningRank from './components/warningRank.vue'
  import { getDictList } from '@/api/system/dict-api'
  import { getProjectAndAlarm } from '@/api/projectMap/index'
  import { structureStatisticsByUser } from './api/safeManage-api'
  export default {
    name: 'safeManagePlatform',
    components: {
      projectMap,
      statisticalInfo,
      projectInformation,
      warningProcessing,
      monitorDeviceInfo,
      monitorVideo,
      warningRank
    },
    data() {
      return {
        scaleX: 1,
        scaleY: 1,
        lock: false,
        myResize: null,

        loading: false,
        keepAlive: '',

        dateTime: '',
        dateYear: '',
        dateWeek: '',

        projectList: [],
        projectTypeList: [],
        allProjectIds: [],
        statisticalInfoData: {},
        videoList: [],
        warningRankList: [],
      }
    },
    computed: {
      ...mapGetters({userId: 'user/userid'}),
      styles() {
        return {
          transform: `scaleX(${this.scaleX}) scaleY(${this.scaleY})`
        }
      }
    },
    async created() {
      this.getComprehensiveData()
      await this.getAllProjectType()
      await this.getProjectList()
    },
    mounted() {
      screenfull.toggle(this.$refs.bigScreen);
      this.myResize = debounce(() => {
        this.initScale()
      }, 300)
      this.listenResize()

      const timer = setInterval(() => {
        const date = parseTime(new Date(), '{y}-{m}-{d} {h}:{i}:{s} {a}')
        this.dateYear = date.substring(0, 10)
        this.dateTime = date.substring(11, 19)
        this.dateWeek = date.substring(20, 21)
      },1000)
      this.$once('hook:beforeDestroy', () => {
        clearInterval(timer)
        // clearTimeout(timeOut)
      })
    },
    beforeDestroy() {

    },
    destroyed() {
      if (this.keepAlive != null) {
        clearInterval(this.keepAlive)
        this.keepAlive = null
      }
    },
    methods: {
      logout() {
        window.removeEventListener('resize', this.myResize)
        screenfull.isFullscreen && screenfull.toggle()
        this.$router.go(-1)
      },
      initScale() {
        const $container = document.querySelector('.container')
        if(!$container) return
        let containerWidth = getComputedStyle($container, 'width').replace('px', '')
        let containerHeight = getComputedStyle($container, 'height').replace('px', '')
        containerWidth = Number(containerWidth)
        containerHeight = Number(containerHeight)
        containerWidth = isNaN(containerWidth) ? 0 : containerWidth
        containerHeight = isNaN(containerHeight) ? 0 : containerHeight

        const defaultHeight = 1080
        const defaultWidth = 1920
        // sacle 缩放比例。
        let scaleX = 1
        let scaleY = 1
        //
        if (this.lock) {
          scaleX = scaleY = containerWidth / defaultWidth
        } else {
          scaleX = containerWidth / defaultWidth
          scaleY = containerHeight / defaultHeight
        }
        this.scaleX = scaleX
        this.scaleY = scaleY
      },
      listenResize() {
        this.initScale()
        window.addEventListener('resize', this.myResize)
      },
      // 获取统计信息
      async getComprehensiveData() {
        const { result } = await structureStatisticsByUser({userId: this.userId})
        const {
          totalProjects,
          totalDevice,
          onlineDevice,
          alarmNum,
          videoConfigNum,
          carWeightNum,
          normalProject,
          abnormalProject,
          videoConfigList,
          projectAlarmSum,
        } = result
        this.statisticalInfoData = {
          totalProjects,
          totalDevice,
          onlineDevice,
          alarmNum,
          videoConfigNum,
          carWeightNum,
          normalProject,
          abnormalProject,
        }
        this.videoList = videoConfigList
        this.warningRankList = projectAlarmSum
      },
      async getProjectList() {
        const { result = [] } = await getProjectAndAlarm({})
        const projectList = []
        const allProjectIds = []
        result.forEach(item => {
          allProjectIds.push(item.id)
          const lnglatData = item.position && item.position.split(',')
          const type = this.getProjectTypeName(item.projectType)
          projectList.push({
            id: item.id,
            name: item.name,
            alarmLevel: item.alarmLevel,
            alarmType: item.alarmType,
            type,
            address: item.address,
            manager: item.manager,
            pic: item.pic ?? require('@/assets/empty_images/data_empty.png'),
            startTime: parseTime(item.startTime, '{y}-{m}-{d}'),
            longitude: lnglatData[0],
            latitude: lnglatData[1],
          })
        })
        this.projectList = projectList
        this.allProjectIds = allProjectIds
      },
      async getAllProjectType() {
        const { result } = await getDictList({ dictCode: 'projectType' })
        this.projectTypeList = result[0].children.map(item => {
          return {
            label: item.dictName,
            value: item.dictCode
          }
        })
      },
      getProjectTypeName(ProjectType) {
        if (!ProjectType) return ''
        for (const data of this.projectTypeList) {
          if (data.value == ProjectType) {
            return data.label
          }
        }
      },
    }
  }
</script>

<style scoped lang="scss">
  @import './css/largeScreen.css';
.wrapper {
  height: 100%;
  width: 100%;
  position: relative;
}
.container {
  background-color: rgb(3, 12, 59);
  width: 100%;
  height: 100%;
  // position: fixed;
  // top: 0;
  // left: 0;
  // right: 0;
  // bottom: 0;
}


.dashboard-container {
  position: relative;
  user-select: none;
  width: 100%;
  height: 100%;
  transform-origin: 0 0;
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, .5);
  transition: all .3s linear;
  overflow: hidden;
}

.dashboard-error {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  z-index: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .dashboard-error-inner {
    color: #fff;
    font-size: 32px;
  }
}
.main-box{
  height: calc(100% - 105px);
  position: relative;
}
.header{
  width: 100%;
  height: 95px;
  background: url(./assets/top_title.png);
  background-repeat: no-repeat;
  background-size: 100% auto;
  position: relative;
}
.title-date{
  position: absolute;
  left: 20px;
  top:15px;
  color: rgba(255,255,255,.9);
  font-size: 16px;
  display: flex;align-items: center;
  .time-text{
    font-size: 40px;
    font-stretch: condensed;
    margin-right: 15px;
    font-weight: bold;
  }
  .date-text{display: block;font-size: 20px;}
  .week-text{display: block;}
}
.title{
  height: 100%;
  width: 800px;
  position: absolute;
  left: 50%;
  top: 32%;
  margin-left: -400px;
  text-align: center;
}
.title-img{
  vertical-align: top;
  display: inline-block;
  margin-top: 4px;
}
.title-txt{
  display: inline-block;
  margin: 0px;
  margin-left: 5px;
  color: #FFFFFF;
  font-size: 34px;
  font-weight: bolder;
  vertical-align: top;
  font-family: '微软雅黑';
  text-shadow: -1px -2px 2px #98d2ff;
}
.close-btn{
  color: #23cefd;
  font-size: 36px;
  position: absolute;
  right: 20px;
  top: 30px;
  cursor: pointer;
  text-shadow: 0px 0px 5px #fff;
}
.weather{
  position: absolute;
  right: 100px;
  top:20px;
  display: flex;align-items: center;
  color: #fff;margin-right:15px;
  img{}
  .temp-text{
    margin-left: 5px;
    font-size: 28px;
    font-stretch: condensed;
    font-weight: bold;
    display: block;
  }
  .weather-text{margin-left: 5px;display: block;font-size: 16px;}
}
  .content-box {
    width: 100%;
    position: relative;
    z-index: 999;
    height: calc(100% - 90px);
    box-sizing: border-box;
    padding: 0 15px 0 15px;
    display: flex;
    justify-content:space-between;

    .left-area,
    .right-area {
      width: 446px;
      height: 100%;
    }
  }

  .footer-box {
    position: absolute;
    width: 100%;
    height: 370px;
    bottom: 0;
    background-image: url('./assets/footer-shadow.png');
    .footer-content {
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 116px;
      background-image: url('./assets/foot_bg.png');
      text-align: center;
      span {
        display: inline-block;
        color: #6cadfb;
        font-size: 16px;
        letter-spacing: .4em;
        padding-top: 85px;
      }
    }
    .project-num {
      position: absolute;
      bottom: 40px;
      width: 100%;
      text-align: center;
      .project-item {
        display: inline-block;
        width: 116px;
        height: 116px;
        background-image: url('./assets/base.png');
        margin-right: 70px;
        &:last-child {
          margin-right: 0;
        }
        .province {
          font-size: 20px;
          color: #2affff;
          padding-top: 10px;
          margin-bottom: 10px;
        }
        .num {
          font-size: 36px;
          color: #cff6f7;
          font-weight: bold;
        }
      }
    }
  }
  .mask {
    position: absolute;
    pointer-events: none;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 100%;
    background: #0000;
    opacity: 0.75;
    box-shadow: inset 1px 0px 500px 150px #011b3b;
  }
</style>
