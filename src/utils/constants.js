export const ROOT = '-1'
// 标段类型 施工
export const construction = 'd1c5565b-c1c0-4cfa-862c-c69544d947a1'
// 标段类型 监理
export const supervisor = 'c502372d-525e-4ce3-b1d5-a30705c1f22b'
// 标段类型 服务
export const service = 'f29a93d1-82b0-4125-9e58-37993cd088f0'
// 报表配置类型 功能
export const functions = 'ffed09d4-54b6-4515-85d6-f355ddd990b0'
// 报表配置类型 报表
export const report = '8c7ab11e-69e0-4f01-8a3f-09edc0e1a8ea'
// 报表配置类型 款项
export const funds = '679c2c4d-a37c-42e7-834b-750ce2708a1c'
// 生效状态
export const effectiveStatus = 'effectiveStatus'
// 客户端标识
export const clientType = 'pc'
// 智慧工地子系统
export const MPAY_SYSTEM = '19b9d85d-1f9a-4082-911a-6725c7e1d872'
// C类设计变更
export const designChangeTypeC = '0edb8aa0-7911-46ef-bb37-7f3fc8d7cd94'
// B类设计变更
export const designChangeTypeB = '6928b4a6-a161-436f-86d1-0eb806ec5299'
// A类设计变更
export const designChangeTypeA = '8ec51e3d-477c-4a80-b15f-0846244e8b1b'
// 较大设计变更
export const moreDesignChange = '64371a11-173a-4314-b4cc-63744854a095'
// 重大设计变更
export const majorDesignChange = 'cf32229e-213e-40ac-bc19-3c04f2a382e0'

// 变更清单类型  变更立项清单
export const changeListingTypeApproval = 'd0ab3c62-b2c3-47e2-8050-dce2ea886695'
// 变更清单类型  变更文件清单
export const changeListingTypeFile = 'a6938fe8-29d8-4f8f-8fe6-de41fef0a3e0'

// 会议纪要类型 变更立项会议纪要
export const meetingTypeApproval = 'c5d5d10f-70c3-4397-9da0-862646b2b17f'
// 会议纪要类型 变更文件会议纪要
export const meetingTypeFile = '63a3c841-6195-43ca-80b5-83c3cb983e78'

// 巡检方式 ping
export const pingInspectType = 'eef05404-7407-4487-ab8a-bcc49f1ac7a8'
// 巡检方式 telnet
export const telnetInspectType = '164f7658-23b9-47e4-8413-b4c9d0cd1771'
// 巡检方式 inf
export const infInspectType = '56d11102-3343-4e67-8679-32a0d0aa09f8'
// 巡检方式 service
export const serviceInspectType = '50f3e79b-f94d-4c95-8ff8-12ceefb7d0fe'

// 设备类型 水泥拌合站
export const cementDeviceType = '64d809eb-8e6f-40aa-8661-9f60e403eeef'

// 设备类型 压力机
export const pressureDeviceType = 'd3fb20c0-91d5-48a5-822c-dd6a6dd99644'

// 设备类型 万能机
export const universalDeviceType = 'e03b12c0-78b0-4172-87ab-5889b2d3606b'

// 计量配置类型 功能
export const functionsMeteringConfigType = '6d40503b-fb37-4b5c-9c75-44b26db2236a'
// 计量配置类型 款项
export const fundsMeteringConfigType = 'ab4c1988-403c-47fb-88bc-f36434b26357'
// 计量配置类型 报表
export const reportMeteringConfigType = 'f6c0e974-db39-45e0-8bc6-293bd01ad85f'

// 计量配置款项计量方式类型 公式计算
export const scriptFundsCountMode = '2413e7c4-ebbe-4859-81a0-a04b9dd06fea'

export const projectType = {
  mpay: 'cf32229e-213e-40ac-bc19-3c04f2a382e0'
}

// 推送数据类型 -- 项目
export const project = 'c18e9806-d0a5-4df4-9064-b6fb30edf3f6'
// 推送数据类型 --标段
export const bidSection = 'fdd31609-a40c-4c44-aa06-1383d0f96102'
// 标段类型 -隧道
export const tunnelType = 'f4148469-c985-4cce-a399-79eafea3610e'
// 三方类型 - 隧道
export const typeThirdTunnel = '58226d39-7b28-4be2-b965-57a2571d8173'

// 设备大类 - 环境监测
export const deviceMainTypeEnvironmentalMonitoring = '580e69fe-00e1-4168-b150-77c0e4797e93'

// 设备类型 - 气体基站
export const baseStationGas = 'c8a21004-cf46-45cf-8ec3-af1c9e4d4ea5'
// 设备类型 - 粉尘基站
export const baseStationDust = '56607dd5-bfa5-44d6-9a57-dcb088483e9f'
// 设备类型 - 温湿度基站
export const baseStationHumiture = '6938ab4a-e797-4643-b4b8-3bd84a41a359'

// 报表类型 - 项目统计表
export const projectStatistics = '26bf295a-d167-4510-aa23-d5fab1f69594'
// 报表类型 - 合同一览表
export const listOfContracts = 'bd76acbe-3beb-4f0f-8cdc-3442b7697100'
// 报表类型 - 计量变更统计表
export const meteringChangeReport = '498ec5f3-04cc-40ba-8f48-3949f35518de'
// 报表类型 - 计量支付统计表
export const meteringReport = '3c557010-ceaf-442f-80c3-f27d4ff7523f'
// 报表类型 - 施工计量支付一览表
export const constructionMeteringReport = '1b703cab-61aa-4e60-90aa-42c5ef76f8f7'
// 报表类型 - 监理计量支付一览表
export const supervisorMetering = '4d9e32b6-8d5c-4753-aa5e-920840199817'
// 报表类型 - 服务类计量支付一览表
export const serviceMetering = '0896aaba-22d7-4c62-845e-9f1419158249'
// 报表类型 - 变更一览表
export const changeFileReport = 'a6b8f3a7-73aa-4700-b62a-dd01d86eee06'
// 报表类型 - 合同管理
export const contract = '240f61e7-bc2c-4091-b29e-e380a5756631'
// 报表类型 - 计量申请
export const fundApply = 'cb05c978-5db3-4aaa-9ed2-8e03e42ad07b'
// 报表类型 - 计量支付
export const fundsManagement = '32c151fa-a5d0-4f99-a6ee-85118d7239b4'
// 报表类型 - 项目月报
export const monthlyProgressReport = '4b100ebf-5530-4f1c-85d1-f130317c2bae'
// 报表类型 - 实际施工进度计划
export const realitySchedule = 'cffafb2a-b905-4f66-842e-8a8f54491487'
// 文件访问地址
export const fileAddr = ''
// 文件存储文件夹名称
export const folderName = 'bridgeMonitoring'

//报表的地址
export const reportAddr = 'https://report.jxth.com.cn'

 export const webSocketUrl = 'ws://220.176.196.195:8120/'
// export const webSocketUrl = 'ws://127.0.0.1:8120/'
// 报表类型 - 项目信息
export const projectBasicInfo = 'dcd229be-2aa9-467e-8b04-1cb263dee8b1'
// 报表类型 -  项目立项
export const projectApprovalReport = '837f20a6-35b7-4b38-8b6a-60b578ffc404'
// 报表类型 -  招标信息
export const bidInfo = '7e375a52-e384-4616-8729-51b348e52ef5'

export const byUser = '539a6d62-f328-489c-88a1-84e7e1b51c0b'
export const byDepart = '9ac22d73-4df1-434c-8ae4-d1ca7390e3a7'
export const byRole = 'c305ab54-31d6-4169-8b20-b7d6cceda660'

export const permissionProject = 'd1d9948c-c603-4470-a7f1-6ca037df75f0'
// 立项项目权限
export const projectApproval = '1e2ddfde-9444-4665-8345-b29fb4db3a55'
// 招标项目权限
export const bidPower = '3ae1ed76-6b67-4db4-9271-edf32c2768c9'

export const allYearSchedule = '35c54ea8-c0f2-4f90-8f17-af1e3397c5df'
export const yearSchedule = 'fbb74bc8-d202-4b99-89cc-7456579cb4d6'
export const monthSchedule = '6b69100c-57ca-4a51-9aa6-7b76b49080ef'

export const startUserId = 'c5714700-51a0-485e-871b-1e6f13f106b8'

// 计量支付类型 计量
export const fundsManagementTypeApply = '1'

// 计量支付类型 支付
export const fundsManagementTypePay = '2'

/**
 * 招标范围
 * BIDSCOPE_EPC EPC
 * BIDSCOPE_DESIGN 设计
 * BIDSCOPE_CONSTRUCTION 施工
 * BIDSCOPE_MANAGE  监理
 */
export const BIDSCOPE_EPC = '0c5e884f-c51c-4e3d-8715-7cad4590d8d3'
export const BIDSCOPE_DESIGN = 'fb0cd28f-ae88-4a83-aba3-959bcffb4cb3'
export const BIDSCOPE_CONSTRUCTION = '113d5bf5-4617-4a2c-829f-ffc2ee40c1af'
export const BIDSCOPE_MANAGE = '5901bf37-b98e-45d7-be36-55bd9e711f5e'

// 是否重大合同 否
export const IS_IMPORTANCE_CONTRACT_YES = 'e6de2447-79ed-49b4-8f21-29ae57d82cfa'

// 是否重大合同 是
export const IS_IMPORTANCE_CONTRACT_NO = 'f544eb23-c841-40d6-b458-54975b063b86'

// 默认字体大小
export const DEFAULT_FONTSIZE = 16

// 合同类型
export const INVESTSTYLE = '75d84a2f-8c5f-49c4-8d4a-4fd86c1e2c65'

// 系统id
export const CITY_SYSTEM = '19b9d85d-1f9a-4082-911a-6725c7e1d872'

// 文件访问地址
export const appFileAddr = 'http://106.227.83.37:9001/'


// 权限关联类型-按用户
export const BY_USER = "539a6d62-f328-489c-88a1-84e7e1b51c0b"

// 权限关联类型-按角色
export const BY_ROLE = "c305ab54-31d6-4169-8b20-b7d6cceda660"

// 权限关联类型-按部门
export const BY_DEPART = "9ac22d73-4df1-434c-8ae4-d1ca7390e3a7"
