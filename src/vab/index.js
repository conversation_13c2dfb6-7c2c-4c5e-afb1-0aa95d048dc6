import Vue from 'vue'

// 加载雪碧图
import '@/icon'
// 加载全局样式样式
import './styles/vab.scss'
import "../assets/App/App.scss";

// 加载VabIcon
import VabIcon from 'vab-icons'
import 'vab-icons/lib/vab-icons.css'

Vue.component('VabIcon', VabIcon)

import dataV from "@jiaminghi/data-view";
Vue.use(dataV);

// import dataV from "@jiaminghi/data-view";
// Vue.use(dataV);

// 大文件上传
import uploader from 'vue-simple-uploader'
Vue.use(uploader)

import scroll from 'vue-seamless-scroll'
Vue.use(scroll)

// 加载主题
const Themes = require.context('./styles/themes', false, /\.scss$/)
Themes.keys().map(Themes)

// 加载插件
const Plugins = require.context('./plugins', true, /\.js$/)
Plugins.keys().map(Plugins)

// 加载组件
const Components = require.context('.', true, /\.vue$/)
Components.keys()
  .map(Components)
  .forEach((item) => {
    if (item.default.name && item.default.name !== 'Layouts')
      Vue.component(item.default.name, item.default)
  })

//加载base64
import * as Base64 from "js-base64"
Vue.prototype.$Base64 = Base64
