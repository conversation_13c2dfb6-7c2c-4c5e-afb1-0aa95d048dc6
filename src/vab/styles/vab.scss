/**
 * @description 全局样式
 */

@import './loading.scss';
@import './normalize.scss';
@import './transition.scss';

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba($base-menu-background, 0.1);
    border: 3px solid transparent;
    border-radius: 7px;
  }
  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba($base-menu-background, 0.2);
  }
}

.vab-layout-header,
[class*='-bar-container'] {
  transition: $base-transition;

  * {
    transition: $base-transition;
  }
}

html {
  body,
  body[class*='vab-theme-'] {
    position: relative;
    box-sizing: border-box;
    height: 100vh;
    padding: 0;
    overflow: hidden;
    font-family: 'PingFang SC', Arial, 'Microsoft YaHei', sans-serif;
    font-size: $base-font-size-default;
    color: $base-color-black;
    background: $base-color-background;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    #app {
      height: 100vh;
      overflow: auto;
      @include base-scrollbar;

      .vab-main {
        transition: $base-transition;

        .vab-app-main {
          width: 100%;
          padding: $base-padding;
          overflow: hidden;
          transition: $base-transition;

          > section {
            background: $base-color-white;
            transition: $base-transition;

            > [class*='-container'] {
              min-height: $base-keep-alive-height;
              padding: $base-padding;
              background: $base-color-white;
              transition: $base-transition;
            }
          }
        }
      }
    }

    * {
      box-sizing: border-box;
      outline: none !important;
      @include base-scrollbar;
    }

    /*a标签 */
    a {
      color: $base-color-blue;
      text-decoration: none;
    }

    /*图片 */
    img {
      object-fit: cover;

      &[src=''],
      &:not([src]) {
        opacity: 0;
      }
    }

    /* vab-fullscreen全屏 */
    .vab-fullscreen {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      z-index: $base-z-index + 1 !important;
      box-sizing: border-box !important;
      width: 100vw !important;
      height: 100vh !important;
      padding-bottom: 15px !important;
      overflow: auto !important;
    }

    /* vab-dropdown下拉动画 */
    .vab-dropdown {
      transition: $base-transition;

      &-active {
        transform: rotateZ(180deg);
      }
    }

    /* vab-dot圆点动画 */
    .vab-dot {
      position: relative;
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 3px;
      vertical-align: middle;
      border-radius: 50%;

      span {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        animation: vabDot 1.2s ease-in-out infinite;
        @keyframes vabDot {
          0% {
            opacity: 0.6;
            transform: scale(0.8);
          }
          to {
            opacity: 0;
            transform: scale(2.4);
          }
        }
      }

      &-success {
        background: $base-color-green;

        span {
          background: $base-color-green;
        }
      }

      &-error {
        background: $base-color-red;

        span {
          background: $base-color-red;
        }
      }
    }

    /* vab-data-empty占位图 */
    .vab-data-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: calc(#{$base-keep-alive-height} - 250px);

      .el-image__inner {
        width: 22.5%;
      }
    }

    /* el-button按钮 */
    .el-button {
      [class*='ri-'] {
        margin-right: 3px;
        font-size: 12px;
        vertical-align: -1.8px;
      }

      [class*='el-icon-'] + span {
        margin-left: 3px;
      }
    }

    span + span,
    a + a,
    span + a {
      .el-button {
        margin-left: 10px;
      }
    }

    .el-drawer__wrapper {
      outline: none !important;

      * {
        outline: none !important;
      }
    }

    /* v-modal遮罩 */
    .v-modal {
      z-index: $base-z-index;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0.6;
      //backdrop-filter: blur(10px);
    }

    /* el-image-viewer遮罩 */
    .el-image-viewer__mask {
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0.6;
      //backdrop-filter: blur(10px);
    }

    /* el-loading-mask遮罩 */
    .el-loading-mask {
      z-index: $base-z-index - 10 !important;

      &.is-fullscreen {
        z-index: $base-z-index + 99 !important;
      }
    }

    /* el-scrollbar滚动条 */
    .el-scrollbar {
      height: 100%;

      &__bar {
        z-index: 999;
      }

      &__thumb {
        background-color: rgba($base-menu-background, 0.1);

        &:hover {
          background-color: rgba($base-menu-background, 0.2);
        }
      }
    }

    /* el-form表单 */
    .el-form--label-top {
      .el-form-item__label {
        padding: 0;
      }
    }

    .el-form-item__label {
      padding: 0 10px 0 0;
    }

    .el-range-editor--small {
      .el-range__icon,
      .el-range__close-icon {
        line-height: 23.5px;
      }
    }

    /*  el-badge */
    .el-badge__content {
      border: 0;
    }

    /* el-tag */
    .el-tag + .el-tag {
      margin-left: 10px;
    }

    /*  .el-page-header */
    .el-page-header {
      margin: 0 0 $base-margin 0;
    }

    /* el-alert */
    .el-alert {
      margin: 0 0 $base-margin 0;

      &--success.is-light {
        color: $base-color-green;
        background-color: mix($base-color-white, $base-color-green, 90%);
        border: 1px solid $base-color-green;

        i {
          color: $base-color-green;
        }
      }

      &--info.is-light {
        color: $base-color-blue;
        background-color: mix($base-color-white, $base-color-blue, 90%);
        border: 1px solid $base-color-blue;

        i {
          color: $base-color-blue;
        }
      }

      &--warning.is-light {
        color: $base-color-yellow;
        background-color: mix($base-color-white, $base-color-yellow, 90%);
        border: 1px solid $base-color-yellow;

        i {
          color: $base-color-yellow;
        }
      }

      &--error.is-light {
        color: $base-color-red;
        background-color: mix($base-color-white, $base-color-red, 90%);
        border: 1px solid $base-color-red;

        i {
          color: $base-color-red;
        }
      }
    }

    /* el-dropdown-menu */
    .el-dropdown-menu__item {
      [class*='ri-'] {
        margin-right: 0;
      }
    }

    /* markdown编辑器*/
    .editor-toolbar {
      .no-mobile,
      .fa-question-circle {
        display: none;
      }
    }

    /* el-divider间隔线 */
    .el-divider--horizontal {
      margin: 8px 0 $base-margin + 8px 0;

      .el-divider__text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    /* nprogress进度条 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index + 3;

      .bar {
        background: $base-color-blue;
      }

      .peg {
        box-shadow: 0 0 10px $base-color-blue, 0 0 5px $base-color-blue;
      }
    }

    /* el-table表格 */
    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }

      th {
        background: #f5f7fa;
      }

      td,
      th {
        position: relative;
        box-sizing: border-box;

        .cell {
          font-size: $base-font-size-default;
          font-weight: normal;
          color: #606266;

          .el-image {
            width: 50px;
            height: 50px;
            border-radius: $base-border-radius;
          }
        }
      }
    }

    /* el-pagination分页 */
    .el-pagination {
      margin: $base-margin 0 0 0;
      font-weight: normal;
      color: $base-color-black;
      text-align: center;
    }

    /* el-menu菜单开始 */
    .el-menu {
      user-select: none;
    }

    /* el-dialog、el-message-box、el-popover */
    @media (max-width: 576px) {
      .el-dialog,
      .el-message-box,
      .el-popover.el-popper {
        width: auto !important;
        margin: 5vw !important;
      }
      .vab-app-main {
        padding: calc(#{$base-padding} - 5px) !important;
        .el-card {
          margin-bottom: calc(#{$base-margin} - 5px) !important;
        }
      }
    }
    /* el-card卡片 */
    .el-card {
      margin-bottom: $base-margin;

      &__body {
        padding: $base-padding;
      }
    }

    /* .vab-hey-message */
    .vab-hey-message {
      @mixin vab-hey-message {
        min-width: 246px;
        padding: 15px;
        background-color: $base-color-white;
        border-color: $base-color-white;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);

        .el-message__content {
          padding-right: $base-padding;
          color: #34495e;
        }

        .el-icon-close {
          color: #34495e;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      &-info {
        @include vab-hey-message;

        i {
          color: $base-color-grey;
        }
      }

      &-success {
        @include vab-hey-message;

        i {
          color: $base-color-green;
        }
      }

      &-warning {
        @include vab-hey-message;

        i {
          color: $base-color-yellow;
        }
      }

      &-error {
        @include vab-hey-message;

        i {
          color: $base-color-red;
        }
      }
    }
  }
}
//表格拖拽样式
.custom-table-checkbox {
  [class*='ri'] {
    vertical-align: -2.5px;
    cursor: pointer;
  }
  .el-checkbox {
    margin: 5px 0 5px 8px;
  }
}
//取消tree最后子节点的接头
.el-tree-node__expand-icon.is-leaf {
	color: transparent!important;
	cursor: default
}

$base: '.depart-management';
#{$base}-container {
  padding: 0 !important;
  background: $base-color-background !important;

  &.vab-fullscreen{
    padding: 20px !important;
  }
}

//自定义表单样式
table.form-table{
  border-collapse: collapse;
  text-align: center;
  width: 100%;
  // tr{background: #fff;}
  td,th{
    border: 1px solid #bbb;
    text-align: left;padding: 0px;
  }
  td{height: 57px;overflow: visible;}
  .title td{
    padding-left: 10px; text-align: center;
    font-weight: bold;
    color: #000;
  }
  .el-form-item{
    display: flex;
    margin: 0px;width: 100%;height: 100%;
    align-items: center;
  }
  .el-form-item__label{
    background-color: #eeeff4;
    width: 200px;padding-top: 10px;padding-bottom: 10px;
    border-right: 1px solid #bbb;height: 100%;
    box-sizing: border-box;
    display: flex;
    justify-content: flex-end;
    align-items: center;    line-height: normal;text-align: right;
  }
  .el-form-item__content{
    width:300px;overflow: visible;overflow: visible;
    padding: 10px;padding-right:30px;
    > div {width: 100%;}
    .el-form-item__error{position: relative;}
  }
}

.el-tabs__nav-next,
.el-tabs__nav-prev {
  background: #f5f5f5;
  padding: 0px 2px;
}

.BMap_cpyCtrl {
  display: none;
}
.anchorBL {
  display: none;
}
