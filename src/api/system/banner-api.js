import request from '@/utils/request'

/**
 * 查询轮播图配置信息
 * @param data
 */
export function getBannerUserUpload(data) {
  return request({
    url: '/proxy/banner/getBannerUserUpload',
    method: 'post',
    data
  })
}
/**
 * 保存轮播图配置信息
 * @param data
 */
export function saveBannerUserUpload(data) {
  return request({
    url: '/proxy/banner/saveBannerUserUpload',
    method: 'post',
    data
  })
}


/**
 * 分页查询轮播图信息
 * @param curPage
 * @param pageSize
 * @param data
 */
export function getBannerByUserIdByPage(curPage, pageSize, data) {
  return request({
    url: 'proxy/banner/getBannerByUserIdByPage/' + curPage + '/' + pageSize,
    method: 'post',
    data
  })
}

/**
 * 获取用户绑定的轮播图信息
 * @param data
 */
export function getImageUrl(data) {
  return request({
    url: '/proxy/banner/getImageUrl',
    method: 'post',
    data
  })
}


