import request from '@/utils/request'

export function getDepartList(data) {
  return request({
    url: '/base/depart/getDataList',
    method: 'post',
    data
  })
}

export function getDepartByPage(data) {
  return request({
    url: '/base/depart/getDataListByPage',
    method: 'post',
    data
  })
}

export function deleteDepart(data) {
  return request({
    url: '/base/depart/deleteData',
    method: 'post',
    data
  })
}

export function saveDepart(data) {
  return request({
    url: '/base/depart/saveData',
    method: 'post',
    data
  })
}

export function getDepart(data) {
  return request({
    url: '/base/depart/getData',
    method: 'post',
    data
  })
}

export function getDepartById(id) {
  return request({
    url: '/base/depart/getData',
    method: 'post',
    data:{id}
  })
}

export function importDeparts(data) {
  return request({
    url: '/base/depart/importDataList',
    method: 'post',
    data
  })
}
