import request from '@/utils/request'

export function getDeviceTypeByProjectId(data) {
  return request({
    url: '/bridge/online/getDeviceTypeByProjectId',
    method: 'post',
    data,
  })
}

export function getHistoryChartData(data) {
  return request({
    url: '/bridge/online/getHistoryChartData',
    method: 'post',
    data,
  })
}

export function getSurveyChartData1(data) {
  return request({
    url: '/bridge/online/getSurveyChartData1',
    method: 'post',
    data,
  })
}

export function getOriginalChartData(data) {
  return request({
    url: '/bridge/online/getOriginalChartData',
    method: 'post',
    data,
  })
}

export function getMaxMinAlarmData(data) {
  return request({
    url: '/bridge/online/getMaxMinAlarmData',
    method: 'post',
    data,
  })
}

export function getDeviceTitleAndUnit(data) {
  return request({
    url: '/bridge/online/getDeviceTitleAndUnit',
    method: 'post',
    data,
  })
}

export function getRoseData(data) {
  return request({
    url: '/bridge/online/getRoseData',
    method: 'post',
    data,
  })
}

export function exportData(data) {
  return request({
    url: '/bridge/online/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}

export function getTypeNumFilter(type, value) {
  let result = ''
  if(type === 'humid_s' || type === 'humid_w'){
    result = numFilter(value, 1)
  }else {
    result = numFilter(value, 2)
  }
  if(result === 'NaN'){
    result = ''
  }
  return result
}


export function numFilter(value, digits) {
  if (value === null || value === '') {
    return ''
  }
  const realVal = parseFloat(value).toFixed(digits)
  return realVal
}

export function getGnssScatter(data) {
  return request({
    url: '/bridge/online/getGnssScatter',
    method: 'post',
    data,
  })
}
