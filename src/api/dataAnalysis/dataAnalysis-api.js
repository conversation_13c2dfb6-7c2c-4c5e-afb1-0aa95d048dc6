import request from '@/utils/request'

export function getJoinDevice(data) {
  return request({
    url: "/bridge/relevance/relationType",
    method: "post",
    data
  });
}

export function getJoinDeviceData(data) {
  return request({
    url: "/bridge/relevance/dataComparison",
    method: "post",
    data
  });
}

// 获取所有的device
// export function getAllDeviceType(data) {
//   return request({
//     url: "api/onlineMonitor/device_type?structureId=" + data,
//     method: "get"
//   });
// }
