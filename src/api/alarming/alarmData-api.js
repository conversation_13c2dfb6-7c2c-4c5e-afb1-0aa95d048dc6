import request from '@/utils/request'

/**
 * 查询预警信息
 * @param curPage
 * @param pageSize
 * @param data
 */
export function getAlarmDataByPage(data) {
  return request({
    url: '/bridge/alarmData/getDataListByPage',
    method: 'post',
    data
  })
}

//添加/编辑设备 saveAlarmData
export function saveAlarmData(data) {
  return request({
    url: '/bridge/alarmData/saveData',
    method: 'post',
    data
  })
}

//批量处理预警信息 updateAlarmData
export function updateAlarmData(data) {
  return request({
    url: '/bridge/alarmData/updateData',
    method: 'post',
    data
  })
}

//删除  deleteAlarmData
export function deleteAlarmData(data) {
  return request({
    url: '/bridge/alarmData/deleteData',
    method: 'post',
    data
  })
}

//处理预警信息
export function deal(data) {
  return request({
    url: '/bridge/alarmData/deal',
    method: 'post',
    data
  })
}
