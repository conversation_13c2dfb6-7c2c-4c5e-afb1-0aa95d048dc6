import request from '@/utils/request'

/**
 * 查询设备管理
 * @param curPage
 * @param pageSize
 * @param data
 */
export function getDeviceManagementByPage(data) {
  return request({
    url: '/bridge/deviceManagement/getDataListByPage',
    method: 'post',
    data
  })
}

//添加/编辑设备 saveDeviceManagement
export function saveDeviceManagement(data) {
  return request({
    url: '/bridge/deviceManagement/saveData',
    method: 'post',
    data
  })
}

//查询设备信息 getDataDeviceManagement
export function getDataDeviceManagement(data) {
  return request({
    url: '/bridge/deviceManagement/getDataList',
    method: 'post',
    data
  })
}

//查询设备信息（筛选数据） getDeviceParamsList
export function getDeviceParamsList(data) {
  return request({
    url: '/bridge/deviceManagement/getDeviceParamsList',
    method: 'post',
    data
  })
}

//查询设备信息(异常数据新增页面） getAbnormalDataList
export function getAbnormalDataList(data) {
  return request({
    url: '/bridge/deviceManagement/getAbnormalDataList',
    method: 'post',
    data
  })
}


//删除  deleteDeviceManagement
export function deleteDeviceManagement(data) {
  return request({
    url: '/bridge/deviceManagement/deleteData',
    method: 'post',
    data
  })
}


//检查是否重复  checkDevice
export function checkDevice(data) {
  return request({
    url: '/bridge/deviceManagement/checkData',
    method: 'post',
    data
  })
}

export function getDeviceByType(data) {
  return request({
    url: '/bridge/deviceManagement/getDeviceByType',
    method: 'post',
    data
  })
}

export function getDeviceType(data) {
  return request({
    url: '/bridge/deviceManagement/getDeviceType',
    method: 'post',
    data
  })
}

export function exportData(data) {
  return request({
    url: '/bridge/deviceManagement/exportData',
    method: 'post',
    responseType: 'blob',
    data,
  })
}
