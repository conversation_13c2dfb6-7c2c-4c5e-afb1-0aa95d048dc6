import request from '@/utils/request'

export function getProjectCountForLevel(data) {
  return request({
    url: '/bridge/alarmData/getProjectCountForLevel',
    method: 'post',
    data,
  })
}

export function getProjectAndAlarm(data) {
  return request({
    url: '/bridge/project/getProjectAndAlarm',
    method: 'post',
    data,
  })
}


export function getProjectTypeSum(data) {
  return request({
    url: '/bridge/project/getProjectTypeSum',
    method: 'post',
    data,
  })
}

export function getProjectAndAlarmByPage(data) {
  return request({
    url: '/bridge/project/getProjectAndAlarmByPage',
    method: 'post',
    data,
  })
}