import request from '@/utils/request'

export function getProjectList(data) {
  return request({
    url: '/bridge/project/getDataList',
    method: 'post',
    data,
  })
}

export function getProjectListWithAuth(data) {
  return request({
    url: '/bridge/project/getDataListWithAuth',
    method: 'post',
    data,
  })
}

export function getProjectByPage(data) {
  return request({
    url: '/bridge/project/getDataListByPage',
    method: 'post',
    data,
  })
}

export function getProjectDesignDocument(data) {
  return request({
    url: '/bridge/project/getProjectDesignDocument',
    method: 'post',
    data,
  })
}

export function getProject(data) {
  return request({
    url: '/bridge/project/getData',
    method: 'post',
    data,
  })
}

export function deleteProject(data) {
  return request({
    url: '/bridge/project/deleteData',
    method: 'post',
    data,
  })
}

export function saveProject(data) {
  return request({
    url: '/bridge/project/saveData',
    method: 'post',
    data,
  })
}

export function checkProject(data) {
  return request({
    url: '/bridge/project/checkData',
    method: 'post',
    data,
  })
}

export function getOverLoadById(data) {
  return request({
    url: '/bridge/project/getOverLoadById',
    method: 'post',
    data,
  })
}

//查询项目信息 getDataProject
export function getDataProject(data) {
  return request({
    url: '/bridge/project/getDataListForAll',
    method: 'post',
    data,
  })
}

//项目-用户关联信息获取
export function getRelProjectList(data) {
  return request({
    url: '/bridge/userProjectRel/getDataList',
    method: 'post',
    data,
  })
}

//项目-用户关联信息 新增/修改
export function getProjectRelsaveData(data) {
  return request({
    url: '/bridge/userProjectRel/saveData',
    method: 'post',
    data,
  })
}
