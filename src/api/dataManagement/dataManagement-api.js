import request from '@/utils/request'

/**
 * 查询设备管理
 * @param curPage
 * @param pageSize
 * @param data
 */
export function getDataManagementByPage(data) {
  return request({
    url: '/bridge/dataManagement/getDataListByPage',
    method: 'post',
    data
  })
}

export function getDataList(data) {
  return request({
    url: '/bridge/dataManagement/getDataList',
    method: 'post',
    data
  })
}

export function getTableHead(data) {
  return request({
    url: '/bridge/dataManagement/getTableHead',
    method: 'post',
    data
  })
}

export function deleteDataManagement(data) {
  return request({
    url: '/bridge/dataManagement/deleteData',
    method: 'post',
    data
  })
}
