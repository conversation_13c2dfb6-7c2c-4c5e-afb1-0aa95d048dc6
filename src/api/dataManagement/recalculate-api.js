import request from '@/utils/request'

/**
 * 查询当前与重计算图表数据
 * @param curPage
 * @param pageSize
 * @param data
 */
export function getFormulaList(data) {
  return request({
    url: 'bridge/recalculate/getFormulaList',
    method: 'post',
    data,
  })
}

/**
 * 查询当前与重计算图表数据
 * @param curPage
 * @param pageSize
 * @param data
 */
export function getHistoryAndRecalculateChartData(data) {
  return request({
    url: 'bridge/recalculate/getHistoryAndRecalculateChartData',
    method: 'post',
    data,
  })
}

/**
 * 批量修改传感器数据期望值
 * @param curPage
 * @param pageSize
 * @param data
 */
export function updateExpectVal(data) {
  return request({
    url: 'bridge/recalculate/updateExpectVal',
    method: 'post',
    data,
  })
}
