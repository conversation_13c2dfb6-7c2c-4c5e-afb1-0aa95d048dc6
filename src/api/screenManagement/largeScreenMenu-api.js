import request from '@/utils/request'
//决策大屏菜单配置分页查询 getDataLargeScreenListByPage
export function getDataLargeScreenListByPage(data) {
  return request({
    url: '/bridge/largeScreenMenu/getDataListByPage',
    method: 'post',
    data
  })
}

//添加决策大屏菜单用户权限 saveLargeScreenMenuUserRel
export function saveLargeScreenMenuUserRel(data) {
  return request({
    url: '/bridge/largeScreenMenuUserRel/saveLargeScreenMenuUserRel',
    method: 'post',
    data
  })
}


//查询该用户拥有的决策大屏菜单权限 getLargeScreenMenuUserRelByUserId
export function getLargeScreenMenuUserRelByUserId(data) {
  return request({
    url: '/bridge/largeScreenMenuUserRel/getLargeScreenMenuUserRelByUserId',
    method: 'post',
    data
  })
}

//保存决策大屏菜单配置 saveDataLargeScreenMenu
export function saveDataLargeScreenMenu(data) {
  return request({
    url: '/bridge/largeScreenMenu/saveData',
    method: 'post',
    data
  })
}

//删除决策大屏菜单配置 deleteDataLargeScreenMenu
export function deleteDataLargeScreenMenu(data) {
  return request({
    url: '/bridge/largeScreenMenu/deleteData',
    method: 'post',
    data
  })
}


//获取决策大屏菜单配置列表 getDataListLargeScreenMenu
export function getDataListLargeScreenMenu(data) {
  return request({
    url: '/bridge/largeScreenMenu/getDataList',
    method: 'post',
    data
  })
}


//查询该用户拥有的决策大屏菜单，如没有返回默认配置 getLargeScreenMenuByUserId
export function getLargeScreenMenuByUserId(data) {
  return request({
    url: '/bridge/largeScreenMenu/getLargeScreenMenuByUserId',
    method: 'post',
    data
  })
}


//添加决策大屏菜单默认配置 saveLargeScreenMenuDefaultConfig
export function saveLargeScreenMenuDefaultConfig(data) {
  return request({
    url: '/bridge/LargeScreenMenuDefaultConfig/saveLargeScreenMenuDefaultConfig',
    method: 'post',
    data
  })
}

//查询决策大屏菜单默认配置 getDataListDefaultConfig
export function getDataListDefaultConfig(data) {
  return request({
    url: '/bridge/LargeScreenMenuDefaultConfig/getDataList',
    method: 'post',
    data
  })
}
