# Vab Admin 系列产品受国家计算机软件著作权保护（证书号：软著登字第 7051316 号）。
# 关于举报盗版侵权：请发送举报材料至我司客服邮箱*****************，一经查实，官司所得收入20%归举报人所有，80%归律师事务所所有。
# Vue Admin系列产品购买地址：https://vue-admin-beautiful.com/authorization
# 1.购买者可将授权后的产品用于任意「符合国家法律法规」的应用平台，禁止用于黄赌毒等危害国家安全与稳定的网站。
# 2.购买主体购买后可用于开发商业项目，不限制域名和项目数量，购买主体不可将源码分享第三方，否则我们有权利收回产品授权及更新权限，并根据事态轻重追究相应法律责任。
# 3.购买者务必尊重知识产权，严格保证不恶意传播产品源码、不得直接对授权的产品本身进行二次转售或倒卖、开源、不得对授权的产品进行简单包装后声称为自己的产品等，无论有意或无意，我们有权利收回产品授权及更新权限，并根据事态轻重追究相应法律责任。
# 4.购买者不可将vip群文档及资料分享给第三方，否则我们有权利收回产品授权及更新权限，并根据事态轻重追究相应法律责任。
# 5.购买者购买项目不可以用来构建存在竞争性质的产品并直接对外销售否则我们有权利收回产品授权及更新权限，并根据事态轻重追究相应法律责任。
# 6.购买者购买项目中的源码（包含全部源码、及部分源码片段）不可以用于任何形式的开源项目，否则我们有权利收回产品授权及更新权限，并根据事态轻重追究相应法律责任。
# 7.用于公司的项目商用时购买需提供公司名称，用于证明购买过我们的项目来用于商业用途，防范法律风险，我们不会将【购买公司】信息泄漏到互联网或告知第三方。
# 8.用于个人学习需提供姓名、联系方式。
# 9.如用于外包项目，购买者购买项目中的源码不可直接对外出售，npm run build编译后的项目不受限制。
# 10.虚拟物品不支持退货退款。
# 11.最终解释权归vab系列著作权人所有。


# 第1步：请在此处配置你的github用户名
  VUE_GITHUB_USER_NAME=godimhzq

# 第2步：请在项目根目录新建一个.env.local的新文件，切记是新建空的文件不是直接拷贝.env文件的内容

# 第3步：.env.local的文件只能有一行不可以换行，购买时生成，格式如下：VUE_APP_SECRET_KEY=XXXXXXX













































































# 以下内容不建议修改建议将VUE_APP_SECRET_KEY配置到【.env.local】中
VUE_APP_SECRET_KEY=preview



