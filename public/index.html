<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
  <head>
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta content="webkit" name="renderer" />
    <meta
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
      name="viewport"
    />
    <!--    <link href="<%= BASE_URL %>favicon.ico" rel="icon" />-->
    <link rel="icon" href="data:;base64,=" />
    <!-- <script
      type="text/javascript"
      src="//api.map.baidu.com/api?v=3.0&ak=Ntkvpr7DFA5i3r5Fzvv4kttmorktoHx6"
    ></script> -->
<!--<script src="//api.map.baidu.com/api?type=webgl&v=1.0&ak=bbi3xezuLecLF4ZZiIMp5cJHz5Wnu0Rw"></script>-->
<!--    替换天地地图-->
    <script src="http://api.tianditu.gov.cn/api?v=4.0&tk=6d6e73ede8d842fd5e52ea92029bbddd" type="text/javascript"></script>

    <title><%= VUE_APP_TITLE %></title>
    <!-- <meta
      content="Vue Admin Plus,Vue Admin Pro,Vab Admin Plus,Vab Admin Pro,vab官网,后台管理框架,vue后台管理框架,vue-admin-beautiful,admin-pro,vue-admin-beautiful官网,vue-admin-beautiful文档,vue-element-admin,vue-element-admin官网,vue-element-admin文档,vue-admin,vue-admin官网,vue-admin文档"
      name="keywords"
    /> -->
    <meta content="<%= VUE_APP_AUTHOR %>" name="author" />
    <link
      href="<%= BASE_URL %>static/css/loading.css?random=<%= VUE_APP_RANDOM %>"
      rel="stylesheet"
    />
    <script>
      window.basePathUrl = '<%= BASE_URL %>'
    </script>
  </head>
  <body>
    <noscript></noscript>
    <div id="app">
      <div class="first-loading-wrp">
        <div class="loading-wrp">
          <span class="dot dot-spin">
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </span>
        </div>
        <h1><%= VUE_APP_TITLE %></h1>
      </div>
    </div>
<!--    百度统计代码-->
<!--    <script>-->
<!--      if (window.location.hostname !== 'localhost') {-->
<!--        var _hmt = _hmt || []-->
<!--        ;(function () {-->
<!--          var hm = document.createElement('script')-->
<!--          hm.src = 'https://hm.baidu.com/hm.js?085e0fa100dbc0e0e42931c16bf3e9e6'-->
<!--          var s = document.getElementsByTagName('script')[0]-->
<!--          s.parentNode.insertBefore(hm, s)-->
<!--        })()-->
<!--      }-->
<!--    </script>-->
  </body>
</html>
