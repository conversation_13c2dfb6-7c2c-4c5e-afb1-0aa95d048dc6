<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.jxth</groupId>
        <artifactId>DasmartTwo</artifactId>
        <version>BASE2-1.0-SNAPSHOT</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.jxth</groupId>
    <artifactId>bridgeMonitoringDasmart</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>bridgeMonitoring-system</module>
    </modules>

    <properties>
        <!-- 定义基础包的版本号 -->
        <BaseDasmart.version>BASE2-1.0-SNAPSHOT</BaseDasmart.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.jxth</groupId>
            <artifactId>baseTwo-client</artifactId>
            <version>${BaseDasmart.version}</version>
        </dependency>
    </dependencies>

</project>
