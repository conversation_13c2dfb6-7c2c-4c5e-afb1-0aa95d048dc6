{"name": "admin-pro", "version": "2.0.11-dev", "private": true, "author": "chuz<PERSON><PERSON>", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "lint:eslint": "eslint {src,mock}/**/*.{vue,js} --fix", "lint:prettier": "prettier {src,mock}/**/*.{html,vue,css,sass,scss,js,md} --write", "lint:stylelint": "stylelint {src,mock}/**/*.{html,vue,css,sass,scss} --fix --cache --cache-location node_modules/.cache/stylelint/", "template": "plop", "build:report": "vue-cli-service build --report", "build:deploy": "start ./deploy.sh", "build:docker": "vue-cli-service build&&docker build --pull --rm -f \"dockerfile\" -t vueadminbeautifulpro:latest \".\"&&docker run --rm -d  -p 80:80/tcp vueadminbeautifulpro:latest", "global:install": "npm install -g nrm,cnpm,npm-check-updates", "globle:update": "ncu -g", "module:install": "npm install --registry=https://registry.npm.taobao.org&&cnpm i image-webpack-loader -D", "module:update": "ncu -u --reject screenfull,@vue/eslint-config-prettier,compression-webpack-plugin,eslint,eslint-plugin-prettier,filemanager-webpack-plugin,sass,sass-loader,webpack --registry https://registry.npm.taobao.org&&npm run module:install", "module:reinstall": "rimraf node_modules&&npm run module:install", "nrm:npm": "nrm use npm", "nrm:taobao": "nrm use taobao"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@logicflow/core": "1.0.2", "@logicflow/extension": "1.0.2", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.20.2", "crypto-js": "4.2.0", "dayjs": "1.10.7", "echarts": "5.2.2", "echarts-stat": "1.2.0", "element-ui": "2.15.7", "file-saver": "2.0.5", "gcoord": "^0.3.2", "html2canvas": "1.4.1", "js-base64": "3.7.3", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "jsplumb": "2.15.6", "lodash": "4.17.21", "mockjs": "1.1.0", "nprogress": "0.2.0", "qs": "6.10.2", "register-service-worker": "1.7.2", "resize-detector": "0.3.0", "screenfull": "5.2.0", "spark-md5": "3.0.2", "three": "^0.131.3", "vab-contextmenu": "0.0.1", "vab-count": "0.0.1", "vab-croppers": "0.0.3", "vab-icons": "file:vab-icons", "vab-magnifier": "0.0.1", "vab-player": "0.0.5", "vab-quill": "0.0.4", "vue": "2.6.14", "vue-baidu-map": "^0.21.22", "vue-i18n": "8.26.8", "vue-json-viewer": "2.2.21", "vue-qr": "4.0.9", "vue-router": "3.5.3", "vue-seamless-scroll": "1.1.23", "vue-simple-uploader": "0.7.6", "vuedraggable": "2.24.3", "vuex": "3.6.2", "xlsx": "0.17.4", "zx-markdown-editor": "0.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "4.5.15", "@vue/cli-plugin-eslint": "4.5.15", "@vue/cli-plugin-pwa": "4.5.15", "@vue/cli-plugin-router": "4.5.15", "@vue/cli-plugin-vuex": "4.5.15", "@vue/cli-service": "4.5.15", "@vue/eslint-config-prettier": "6.0.0", "body-parser": "1.19.1", "chalk": "4.1.2", "chokidar": "3.5.3", "compression-webpack-plugin": "6.1.1", "eslint": "6.8.0", "eslint-plugin-prettier": "3.4.1", "eslint-plugin-vue": "8.2.0", "filemanager-webpack-plugin": "3.1.1", "image-webpack-loader": "8.0.1", "lint-staged": "12.1.7", "plop": "3.0.5", "postcss": "8.4.5", "postcss-html": "1.3.0", "postcss-jsx": "0.36.4", "postcss-scss": "4.0.2", "postcss-syntax": "0.36.2", "prettier": "2.5.1", "raw-loader": "4.0.2", "sass": "1.49.7", "sass-loader": "10.2.0", "stylelint": "14.2.0", "stylelint-config-prettier": "9.0.3", "stylelint-config-recess-order": "3.0.0", "svg-sprite-loader": "6.0.11", "vab-templates": "0.0.5", "vue-eslint-parser": "8.0.1", "vue-template-compiler": "2.6.14", "webpack": "4.46.0", "webpackbar": "5.0.2"}, "gitHooks": {"pre-commit": "lint-staged"}, "homepage": "https://chu1204505056.gitee.io/admin-pro", "license": "Mozilla Public License Version 2.0", "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "participants": ["LiufengFish"], "repository": {"type": "git", "url": "git+https://github.com/vue-admin-beautiful/admin-pro.git"}}